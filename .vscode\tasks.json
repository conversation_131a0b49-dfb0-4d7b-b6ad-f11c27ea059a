// See https://go.microsoft.com/fwlink/?LinkId=733558
// for the documentation about the tasks.json format
{
	"version": "2.0.0",
	"tasks": [
		{
			"label": "compile-standalone",
			"type": "npm",
			"script": "compile-standalone",
			"group": "build",
			"problemMatcher": [],
			"presentation": {
				"reveal": "always"
			}
		},
		{
			"label": "npm: protos",
			"type": "npm",
			"script": "protos",
			"problemMatcher": [],
			"isBackground": false,
			"presentation": {
				"reveal": "always"
			},
			"options": {
				"env": {
					"IS_DEV": "true"
				}
			}
		},
		{
			"label": "watch",
			"dependsOn": ["npm: protos", "npm: build:webview", "npm: dev:webview", "npm: watch:tsc", "npm: watch:esbuild"],
			"presentation": {
				"reveal": "always"
			},
			"group": {
				"kind": "build",
				"isDefault": true
			}
		},
		{
			"label": "watch:test",
			"dependsOn": [
				"npm: protos",
				"npm: build:webview:test",
				"npm: dev:webview",
				"npm: watch:tsc",
				"npm: watch:esbuild:test"
			],
			"presentation": {
				"reveal": "always"
			},
			"group": "build"
		},
		{
			"type": "npm",
			"script": "build:webview",
			"group": "build",
			"problemMatcher": [],
			"isBackground": true,
			"label": "npm: build:webview",
			"dependsOn": ["npm: protos"],
			"presentation": {
				"group": "watch",
				"reveal": "always"
			},
			"options": {
				"env": {
					"IS_DEV": "true"
				}
			}
		},
		{
			"type": "npm",
			"script": "build:webview:test",
			"group": "build",
			"problemMatcher": [],
			"isBackground": true,
			"label": "npm: build:webview:test",
			"dependsOn": ["npm: protos"],
			"presentation": {
				"group": "watch",
				"reveal": "always"
			},
			"options": {
				"env": {
					"IS_DEV": "true",
					"IS_TEST": "true"
				}
			}
		},
		{
			"type": "npm",
			"script": "dev:webview",
			"group": "build",
			"problemMatcher": [
				{
					"pattern": [
						{
							"regexp": ".",
							"file": 1,
							"location": 2,
							"message": 3
						}
					],
					"background": {
						"activeOnStart": true,
						"beginsPattern": ".",
						"endsPattern": "."
					}
				}
			],
			"isBackground": true,
			"label": "npm: dev:webview",
			"dependsOn": ["npm: protos"],
			"presentation": {
				"group": "watch",
				"reveal": "always"
			},
			"options": {
				"env": {
					"IS_DEV": "true"
				}
			}
		},
		{
			"type": "npm",
			"script": "watch:esbuild",
			"group": "build",
			"problemMatcher": {
				"pattern": [
					{
						"regexp": "^✘ \\[ERROR\\] (.*)$",
						"message": 1
					},
					{
						"regexp": "^\\s+(.*):(\\d+):(\\d+):$",
						"file": 1,
						"line": 2,
						"column": 3
					}
				],
				"background": {
					"activeOnStart": true,
					"beginsPattern": "^\\[watch\\] build started$",
					"endsPattern": "^\\[watch\\] build finished$"
				}
			},
			"isBackground": true,
			"label": "npm: watch:esbuild",
			"dependsOn": ["npm: protos"],
			"presentation": {
				"group": "watch",
				"reveal": "always"
			},
			"options": {
				"env": {
					"IS_DEV": "true"
				}
			}
		},
		{
			"type": "npm",
			"script": "watch:esbuild:test",
			"group": "build",
			"problemMatcher": {
				"pattern": [
					{
						"regexp": "^✘ \\[ERROR\\] (.*)$",
						"message": 1
					},
					{
						"regexp": "^\\s+(.*):(\\d+):(\\d+):$",
						"file": 1,
						"line": 2,
						"column": 3
					}
				],
				"background": {
					"activeOnStart": true,
					"beginsPattern": "^\\[watch\\] build started$",
					"endsPattern": "^\\[watch\\] build finished$"
				}
			},
			"isBackground": true,
			"label": "npm: watch:esbuild:test",
			"dependsOn": ["npm: protos"],
			"presentation": {
				"group": "watch",
				"reveal": "always"
			},
			"options": {
				"env": {
					"IS_DEV": "true",
					"IS_TEST": "true"
				}
			}
		},
		{
			"type": "npm",
			"script": "watch:tsc",
			"group": "build",
			"problemMatcher": "$tsc-watch",
			"isBackground": true,
			"label": "npm: watch:tsc",
			"dependsOn": ["npm: protos"],
			"presentation": {
				"group": "watch",
				"reveal": "always"
			}
		},
		{
			"type": "npm",
			"script": "watch-tests",
			"problemMatcher": "$tsc-watch",
			"isBackground": true,
			"dependsOn": ["npm: protos"],
			"presentation": {
				"reveal": "always",
				"group": "watchers"
			},
			"group": "build"
		},
		{
			"label": "tasks: watch-tests",
			"dependsOn": ["npm: protos", "npm: watch", "npm: watch-tests"],
			"problemMatcher": []
		},
		{
			"label": "stop",
			"command": "echo ${input:terminate}",
			"type": "shell"
		},
		{
			"label": "clean-sandbox",
			"type": "shell",
			"dependsOn": ["watch"],
			"command": "rm -rf .vscode-dev"
		}
	],
	"inputs": [
		{
			"id": "terminate",
			"type": "command",
			"command": "workbench.action.tasks.terminate",
			"args": "terminateAll"
		}
	]
}
