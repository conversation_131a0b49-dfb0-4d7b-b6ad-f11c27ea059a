// AIDIFF: Updated to align with continue/core/autocomplete/templating/AutocompleteTemplate.ts
// PLANREF: continue/core/autocomplete/templating/AutocompleteTemplate.ts
// Fill in the middle prompts

import * as vscode from "vscode"
import { CompletionOptions } from "../types"
import { AutocompleteSnippet, AutocompleteSnippetType } from "./snippetTypes"
import { CodeContext, CodeContextDefinition } from "../ContextGatherer"
import { extractIntelligentContext } from "../utils/contextExtractor"

// AIDIFF: Updated interface to match continue/
export interface AutocompleteTemplate {
	compilePrefixSuffix?: (prefix: string, suffix: string) => [string, string]
	getSystemPrompt: () => string
	template: (
		codeContext: CodeContext,
		document: vscode.TextDocument,
		position: vscode.Position,
		snippets: AutocompleteSnippet[],
	) => Promise<string>
	completionOptions?: Partial<CompletionOptions>
}

// PLANREF: continue/core/autocomplete/templating/AutocompleteTemplate.ts (gptAutocompleteTemplate)
// const gptAutocompleteTemplate: AutocompleteTemplate = {
// 	template: `\`\`\`
// {{{prefix}}}[BLANK]{{{suffix}}}
// \`\`\`
//
// Fill in the blank to complete the code block. Your response should include only the code to replace [BLANK], without surrounding backticks.`,
// 	completionOptions: { stop: ["\n"] },
// }

// PLANREF: continue/core/autocomplete/templating/AutocompleteTemplate.ts (holeFillerTemplate)
export const holeFillerTemplate: AutocompleteTemplate = {
	getSystemPrompt: () => {
		// From https://github.com/VictorTaelin/AI-scripts
		const SYSTEM_MSG = `You are given a code snippet containing a placeholder {{FILL_HERE}}. Your task is to replace it with EXACTLY one block of content while strictly following these rules:
1. If the placeholder appears DIRECTLY before a function/class definition, you may insert ONE language-standard comment describing ONLY that immediately following item.
2. In other case, you should fully analyze the context and replace the placeholder with best fitting content and maintain the code's integrity and style.
3. NEVER:
* Add any code/comment for symbols used but not shown (they're pre-implemented)
* Modify any existing implementation
* Add more than one content block
* Include implementation details in docstrings
4. When uncertain, default to the most minimal compliant option (empty line).
`
		return SYSTEM_MSG
	},
	template: async (
		codeContext: CodeContext,
		document: vscode.TextDocument,
		position: vscode.Position,
		snippets: AutocompleteSnippet[],
	) => {
		// Use intelligent context extraction instead of full file content
		const extractedContext = await extractIntelligentContext(document, position, 100)

		// Insert the fill placeholder at the cursor position within the context
		const cursorOffsetInContext =
			extractedContext.contextCode.split("\n").slice(0, extractedContext.cursorLineInContext).join("\n").length +
			(extractedContext.cursorLineInContext > 0 ? 1 : 0) + // Add 1 for newline if not first line
			extractedContext.cursorCharInContext

		const currentFileWithFillPlaceholder =
			extractedContext.contextCode.slice(0, cursorOffsetInContext) +
			"{{FILL_HERE}}" +
			extractedContext.contextCode.slice(cursorOffsetInContext)

		const queryContextStrings: string[] = []

		// Include import statements from both sources for comprehensive coverage
		const allImportStrings: string[] = []

		// 1. Include imports from the simple regex-based extraction (broader language support)
		if (codeContext.imports && codeContext.imports.length > 0) {
			allImportStrings.push(...codeContext.imports)
		}

		// 2. Include import statements from tree-sitter analysis (more precise, with metadata)
		const importStatements = codeContext.definitions?.filter((item) => item.source === "import_statement") || []
		if (importStatements.length > 0) {
			const treeImportStrings = importStatements.map((item: CodeContextDefinition) => item.content.trim())
			allImportStrings.push(...treeImportStrings)
		}

		// Deduplicate import strings and add to context
		if (allImportStrings.length > 0) {
			const uniqueImports = Array.from(new Set(allImportStrings))
			queryContextStrings.push(`// Import statements:\n${uniqueImports.join("\n")}`)
		}

		// Include other code context definitions (function definitions, etc.)
		const codeContextItems = codeContext.definitions?.filter((item) => item.source !== "import_statement") || []
		if (codeContextItems.length > 0) {
			// AIDIFF: Ensure item.name is correct, CodeContextDefinition has filepath
			const contextItemStrings = codeContextItems.map(
				(item: CodeContextDefinition) => `// File: ${item.filepath}\n${item.content}`,
			)
			queryContextStrings.push(`// Context from other parts of the project:\n${contextItemStrings.join("\n\n")}`)
		}

		if (snippets && snippets.length > 0) {
			const snippetStrings = snippets.map((snippet) => {
				let header = `// Some context: ${snippet.type})`
				if ("filepath" in snippet && (snippet as any).filepath) {
					header = `// Some context: ${snippet.type}) from: ${(snippet as any).filepath}`
				} else if (
					snippet.type === AutocompleteSnippetType.Clipboard &&
					"copiedAt" in snippet &&
					(snippet as any).copiedAt
				) {
					header = `// Some context: ${snippet.type}, copiedAt: ${(snippet as any).copiedAt})`
				}
				return `${header}\n${snippet.content}`
			})
			queryContextStrings.push(`// Relevant snippets:\n${snippetStrings.join("\n\n")}`)
		}

		// Add the current file with hole last, as it's the primary focus
		queryContextStrings.push(`// Current file content with hole:\n${currentFileWithFillPlaceholder}`)

		const queryContent = queryContextStrings.join("\n\n")

		const userPrompt = `\n\n<QUERY>\n${queryContent}\n</QUERY>\nTASK: Fill the {{FILL_HERE}} hole. Answer only with the CORRECT completion, and NOTHING ELSE. Do it now.\n<COMPLETION>`
		return userPrompt
	},
	completionOptions: {
		stop: ["</COMPLETION>"],
	},
}
