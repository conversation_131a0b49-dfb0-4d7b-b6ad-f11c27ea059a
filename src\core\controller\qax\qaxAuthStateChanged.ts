/**
 * <AUTHOR>
 */

import { QAXAccountService } from "../../../services/qax"
import type { EmptyRequest } from "../../../shared/proto/common"
import { QAXAuthStateChanged } from "../../../shared/proto/qax"
import type { Controller } from "../index"

export async function qaxAuthStateChanged(controller: Controller, _request: EmptyRequest): Promise<QAXAuthStateChanged> {
	const qaxService = new QAXAccountService(controller.context)

	try {
		const userInfo = await qaxService.getCurrentUser()
		return QAXAuthStateChanged.create({ user: userInfo || undefined })
	} catch (error) {
		return QAXAuthStateChanged.create({ user: undefined })
	}
}
