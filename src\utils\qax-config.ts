import { QAXJWTPayload } from "@shared/QAXAccount"

/**
 * QAX 配置工具类
 * 提供统一的域名配置和 URL 生成功能
 * <AUTHOR>
 */

export class QAXConfig {
	/**
	 * 默认模型常量
	 */
	static readonly DEFAULT_QAX_CODEGEN_MODEL = "qax-codegen/DeepSeek-V3-0324"
	static readonly DEFAULT_QAX_CODEGEN_MODELS = ["qax-codegen/DeepSeek-V3-0324", "qax-codegen-openai/DeepSeek-R1"]

	/**
	 * 获取 QAX Codegen 域名
	 * 优先级：环境变量 > 环境配置 > 默认值
	 */
	static getCodegenDomain(): string {
		// 优先使用自定义域名
		if (process.env.QAX_CODEGEN_DOMAIN) {
			return process.env.QAX_CODEGEN_DOMAIN
		}

		// 根据环境选择预设域名
		const environment = process.env.QAX_ENVIRONMENT || process.env.NODE_ENV

		switch (environment) {
			case "development":
			case "dev":
				return "https://codegen-dev.qianxin-inc.cn"
			case "testing":
			case "test":
				return "https://codegen-test.qianxin-inc.cn"
			case "production":
			case "prod":
			default:
				return "https://codegen.qianxin-inc.cn"
		}
	}

	/**
	 * 获取 QAX Codegen API 基础 URL
	 */
	static getCodegenApiBaseUrl(): string {
		const domain = this.getCodegenDomain()
		return `${domain}/api/v1`
	}

	/**
	 * 获取 QAX Codegen 登录 URL
	 */
	static getCodegenLoginUrl(): string {
		const domain = this.getCodegenDomain()
		return `${domain}/api/v1/auth/sso/login`
	}

	/**
	 * 获取 QAX Codegen 认证 URL（带回调参数）
	 */
	static getCodegenAuthUrl(ref: string): string {
		const loginUrl = this.getCodegenLoginUrl()
		return `${loginUrl}?ref=${ref}`
	}

	/**
	 * 获取 QAX 模型列表 API URL
	 * 始终使用固定的 v1 接口，不使用用户配置的 baseUrl
	 */
	static getQaxModelsApiUrl(): string {
		return "https://aip.b.qianxin-inc.cn/v1/chat/models"
	}

	/**
	 * JWT Token 工具方法
	 */

	/**
	 * 验证 JWT token 格式是否正确
	 * @param token JWT token
	 * @returns 是否为有效格式
	 */
	static isValidJWTFormat(token: string): boolean {
		if (!token) {
			return false
		}
		const parts = token.split(".")
		return parts.length === 3
	}

	/**
	 * 检查 JWT token 是否过期
	 * @param token JWT token
	 * @returns 是否过期
	 */
	static isTokenExpired(token: string): boolean {
		try {
			const payload = this.decodeJWTToken(token)
			if (!payload) {
				return true
			}
			return Date.now() >= payload.exp * 1000
		} catch {
			return true
		}
	}

	/**
	 * 解码 JWT token 获取 payload
	 * @param token JWT token
	 * @returns 解码后的 payload 或 null
	 */
	static decodeJWTToken(token: string): QAXJWTPayload | null {
		try {
			const parts = token.split(".")
			if (parts.length !== 3) {
				return null
			}

			const payload = JSON.parse(Buffer.from(parts[1], "base64").toString())
			return payload as QAXJWTPayload
		} catch {
			return null
		}
	}

	/**
	 * 验证 JWT token 是否有效（格式正确且未过期）
	 * @param token JWT token
	 * @returns 是否有效
	 */
	static isValidJWTToken(token: string): boolean {
		return this.isValidJWTFormat(token) && !this.isTokenExpired(token)
	}

	/**
	 * 验证 JWT token 并抛出详细错误信息
	 * @param token JWT token
	 * @throws Error 如果 token 无效
	 */
	static validateJWTToken(token: string): void {
		if (!token) {
			throw new Error("QAX Codegen token not found. Please login to QAX Account first.")
		}

		if (!this.isValidJWTFormat(token)) {
			throw new Error("Invalid QAX Codegen token format")
		}

		if (this.isTokenExpired(token)) {
			throw new Error("QAX Codegen token has expired. Please login again.")
		}
	}
}
