/**
 * Test runner for ContextGatherer comprehensive tests
 * Run this file to execute all context gathering functionality tests
 */

import { runSimpleContextTests } from "./SimpleContextTest"

/**
 * Main test execution function
 */
async function main(): Promise<void> {
	console.log("🚀 Starting ContextGatherer Test Suite...")
	console.log("This will test all enhanced context gathering functionality including:")
	console.log("  - Import-based definitions (tree-sitter + LSP)")
	console.log("  - Recently visited function context")
	console.log("  - Clipboard content integration")
	console.log("  - Context prioritization and deduplication")
	console.log("  - Function comment inclusion")
	console.log("  - Edge cases and error handling")
	console.log("  - Complete context assembly validation")
	console.log("")

	try {
		await runSimpleContextTests()
		console.log("\n✅ Test suite completed successfully!")
	} catch (error) {
		console.error("\n❌ Test suite failed with error:", error)
		process.exit(1)
	}
}

// Run the tests if this file is executed directly
if (require.main === module) {
	main().catch((error) => {
		console.error("Fatal error running tests:", error)
		process.exit(1)
	})
}

export { main as runAllTests }
