syntax = "proto3";

package cline;
option java_package = "bot.cline.proto";
option java_multiple_files = true;

import "common.proto";

// QAX Account Service - 简化版本，只保留核心功能
service QaxService {
  // 处理 QAX 用户登录
  rpc qaxLoginClicked(EmptyRequest) returns (String);

  // 处理 QAX 用户登出
  rpc qaxLogoutClicked(EmptyRequest) returns (Empty);

  // QAX 认证状态变更
  rpc qaxAuthStateChanged(EmptyRequest) returns (QAXAuthStateChanged);

  // 订阅 QAX 认证回调事件
  rpc subscribeToQaxAuthCallback(EmptyRequest) returns (stream String);
}

// QAX 认证状态变更响应
message QAXAuthStateChanged { optional QAXUserInfo user = 1; }

// QAX 用户信息 - 完整版本，与TypeScript接口保持一致
message QAXUserInfo {
  optional string user_id = 1;
  optional string username = 2;
  optional string email = 3;
  optional string display_name = 4;
  optional string employee_number = 5;
  optional int64 last_login_at = 6;
}
