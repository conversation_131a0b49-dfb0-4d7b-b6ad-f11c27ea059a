/*
- class declarations
- function definitions
- method declarations
*/
export const definitionQuery = `
(class_declaration
  name: (name) @name.definition.class) @definition.class

(function_definition
  name: (name) @name.definition.function) @definition.function

(method_declaration
  name: (name) @name.definition.function) @definition.function
`

/*
- PHP use statements and require/include statements
*/
export const importQuery = `
; use Namespace\Class;
(use_declaration
  (qualified_name) @import.name) @import.statement

; use Namespace\Class as Alias;
(use_declaration
  (use_as_clause
    (qualified_name) @import.name
    (name) @import.alias)) @import.statement

; use function Namespace\function;
(use_declaration
  "function"
  (qualified_name) @import.name) @import.statement

; use const Namespace\CONSTANT;
(use_declaration
  "const"
  (qualified_name) @import.name) @import.statement

; require 'file.php'
(expression_statement
  (require_expression
    (string) @import.source)) @import.statement

; require_once 'file.php'
(expression_statement
  (require_once_expression
    (string) @import.source)) @import.statement

; include 'file.php'
(expression_statement
  (include_expression
    (string) @import.source)) @import.statement

; include_once 'file.php'
(expression_statement
  (include_once_expression
    (string) @import.source)) @import.statement
`

// Default export for backward compatibility
export default definitionQuery
