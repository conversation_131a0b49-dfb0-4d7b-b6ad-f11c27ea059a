/**
 * Test query syntax directly without loading the full module
 */

console.log("🔍 Starting Query Syntax Validation Test...")

// Mock Parser for syntax testing
const mockParser = {
	query: (queryString) => {
		// Simple syntax validation - check for basic query structure
		if (!queryString || typeof queryString !== "string") {
			throw new Error("Query must be a non-empty string")
		}

		// Check for basic tree-sitter query syntax
		const hasCaptures = queryString.includes("@")
		const hasParentheses = queryString.includes("(") && queryString.includes(")")

		if (!hasCaptures) {
			throw new Error("Query must contain capture groups (@)")
		}

		if (!hasParentheses) {
			throw new Error("Query must contain parentheses for node matching")
		}

		// Check for unmatched parentheses
		let parenCount = 0
		for (const char of queryString) {
			if (char === "(") parenCount++
			if (char === ")") parenCount--
			if (parenCount < 0) {
				throw new Error("Unmatched closing parenthesis")
			}
		}

		if (parenCount !== 0) {
			throw new Error("Unmatched opening parenthesis")
		}

		return { valid: true }
	},
}

async function testQuerySyntax() {
	const fs = require("fs")
	const path = require("path")

	const queryFiles = [
		"src/services/tree-sitter/queries/javascript.ts",
		"src/services/tree-sitter/queries/typescript.ts",
		"src/services/tree-sitter/queries/python.ts",
		"src/services/tree-sitter/queries/rust.ts",
		"src/services/tree-sitter/queries/go.ts",
		"src/services/tree-sitter/queries/java.ts",
		"src/services/tree-sitter/queries/cpp.ts",
		"src/services/tree-sitter/queries/c.ts",
		"src/services/tree-sitter/queries/c-sharp.ts",
		"src/services/tree-sitter/queries/ruby.ts",
		"src/services/tree-sitter/queries/php.ts",
		"src/services/tree-sitter/queries/swift.ts",
		"src/services/tree-sitter/queries/kotlin.ts",
	]

	console.log("\n📋 Testing Query Syntax for All Languages...")
	console.log("=".repeat(60))

	let successCount = 0
	let errorCount = 0
	const errors = []

	for (const filePath of queryFiles) {
		const language = path.basename(filePath, ".ts")

		try {
			console.log(`\n🧪 Testing ${language}...`)

			const content = fs.readFileSync(filePath, "utf8")

			// Extract import query
			const importQueryMatch = content.match(/export const importQuery = `([\s\S]*?)`/)

			if (!importQueryMatch) {
				console.log(`  ⚠️  ${language}: No import query found`)
				continue
			}

			const importQuery = importQueryMatch[1]
			console.log(`  📝 ${language}: Import query found (${importQuery.length} chars)`)

			// Test query syntax
			try {
				mockParser.query(importQuery)
				console.log(`  ✅ ${language}: Query syntax valid`)
				successCount++
			} catch (syntaxError) {
				console.log(`  ❌ ${language}: Query syntax error - ${syntaxError.message}`)
				console.log(`  📄 Query preview: ${importQuery.substring(0, 100)}...`)
				errors.push({
					language,
					error: syntaxError.message,
					query: importQuery.substring(0, 200),
				})
				errorCount++
			}
		} catch (error) {
			console.log(`  ❌ ${language}: File error - ${error.message}`)
			errors.push({
				language,
				error: `File error: ${error.message}`,
				query: null,
			})
			errorCount++
		}
	}

	console.log("\n" + "=".repeat(60))
	console.log("📊 QUERY SYNTAX TEST RESULTS")
	console.log("=".repeat(60))

	console.log(`✅ Valid syntax: ${successCount}/${queryFiles.length}`)
	console.log(`❌ Syntax errors: ${errorCount}/${queryFiles.length}`)

	if (errors.length > 0) {
		console.log("\n🚨 DETAILED ERROR ANALYSIS:")
		console.log("-".repeat(60))

		errors.forEach((err, index) => {
			console.log(`\n${index + 1}. ${err.language}:`)
			console.log(`   Error: ${err.error}`)
			if (err.query) {
				console.log(`   Query: ${err.query}...`)
			}
		})

		// Group errors by type
		const errorTypes = {}
		errors.forEach((err) => {
			const errorType = err.error.split(":")[0] || "Unknown"
			if (!errorTypes[errorType]) {
				errorTypes[errorType] = []
			}
			errorTypes[errorType].push(err.language)
		})

		console.log("\n📈 ERROR PATTERNS:")
		console.log("-".repeat(40))
		Object.entries(errorTypes).forEach(([type, languages]) => {
			console.log(`${type}: ${languages.join(", ")}`)
		})
	}

	console.log("\n" + "=".repeat(60))
	console.log("🏁 SYNTAX TEST COMPLETED")
	console.log("=".repeat(60))

	return { successCount, errorCount, errors }
}

// Run the test
testQuerySyntax()
	.then((results) => {
		if (results.errorCount > 0) {
			console.log(`\n🔧 NEXT STEPS: Fix ${results.errorCount} query syntax errors`)
			process.exit(1)
		} else {
			console.log("\n🎉 All query syntax is valid!")
			process.exit(0)
		}
	})
	.catch((error) => {
		console.error("💥 Unhandled error:", error)
		process.exit(1)
	})
