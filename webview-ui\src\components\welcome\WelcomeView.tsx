import ClineLogoWhite from "@/assets/ClineLogoWhite"
import ApiOptions from "@/components/settings/ApiOptions"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { useQaxAuth } from "@/hooks/useQaxAuth"
import { StateServiceClient } from "@/services/grpc-client"
import { validateApiConfiguration } from "@/utils/validate"
import { BooleanRequest } from "@shared/proto/common"
import { VSCodeButton, VSCodeProgressRing } from "@vscode/webview-ui-toolkit/react"
import { memo, useEffect, useState } from "react"

const WelcomeView = memo(() => {
	const { apiConfiguration } = useExtensionState()
	const [apiErrorMessage, setApiErrorMessage] = useState<string | undefined>(undefined)
	const [showApiOptions] = useState(false)

	// 使用 QAX 认证 Hook
	const { qaxUser, isAuthenticated, isLoading, handleLogin } = useQaxAuth()

	const disableLetsGoButton = apiErrorMessage != null

	const handleSubmit = async () => {
		try {
			await StateServiceClient.setWelcomeViewCompleted(BooleanRequest.create({ value: true }))
		} catch (error) {
			console.error("Failed to update API configuration or complete welcome view:", error)
		}
	}

	useEffect(() => {
		setApiErrorMessage(validateApiConfiguration(apiConfiguration))
	}, [apiConfiguration])

	return (
		<div className="fixed inset-0 p-0 flex flex-col">
			<div className="h-full px-5 overflow-auto">
				<h2>我是 QAXCodegen</h2>
				<div className="flex justify-center my-5">
					<ClineLogoWhite className="size-16" />
				</div>
				<p>
					我是一个智能代码生成助手，具备强大的代码编写能力。我可以帮助您创建和编辑文件、探索复杂项目、使用浏览器以及执行终端命令{" "}
					<i>（当然，需要您的授权）</i>。我还可以使用 MCP 创建新工具并扩展自己的能力。
				</p>

				{!isAuthenticated ? (
					<>
						<p className="text-[var(--vscode-descriptionForeground)]">登录 QAX Account 开始免费使用。</p>

						<VSCodeButton appearance="primary" onClick={handleLogin} disabled={isLoading} className="w-full mt-1">
							{isLoading ? (
								<div className="flex items-center gap-2">
									<VSCodeProgressRing style={{ width: "16px", height: "16px" }} />
									连接中...
								</div>
							) : (
								"使用 QAX Account 登录"
							)}
						</VSCodeButton>
					</>
				) : (
					<>
						<p className="text-[var(--vscode-descriptionForeground)]">
							欢迎回来，{qaxUser?.displayName || qaxUser?.email || "用户"}！
						</p>

						<VSCodeButton appearance="primary" onClick={handleSubmit} className="w-full mt-1">
							开始使用
						</VSCodeButton>
					</>
				)}

				<div className="mt-4.5">
					{showApiOptions && (
						<div>
							<ApiOptions showModelOptions={false} />
							<VSCodeButton onClick={handleSubmit} disabled={disableLetsGoButton} className="mt-0.75">
								Let's go!
							</VSCodeButton>
						</div>
					)}
				</div>
			</div>
		</div>
	)
})

export default WelcomeView
