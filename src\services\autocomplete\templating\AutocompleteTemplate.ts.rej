diff a/src/services/autocomplete/templating/AutocompleteTemplate.ts b/src/services/autocomplete/templating/AutocompleteTemplate.ts	(rejected hunks)
@@ -35,101 +35,15 @@ export interface AutocompleteTemplate {
 export const holeFillerTemplate: AutocompleteTemplate = {
 	getSystemPrompt: () => {
 		// From https://github.com/VictorTaelin/AI-scripts
-		const SYSTEM_MSG = `You are a code completion assistant. When filling in code at {{FILL_HERE}}, you should:
-
-1. **Analyze the full context**: Look at the surrounding code structure, existing patterns, and the specific location of the fill marker
-2. **Follow existing comment patterns**: If the codebase has established commenting conventions (like '// interface', '// implementation details', etc.), maintain consistency
-3. **Identify function importance**: Important functions, especially those that serve as main entry points or implement performance-critical features, typically need descriptive comments
-4. **Consider code organization**: Comments often separate different sections of code (interfaces, implementations, utilities, etc.)
-
-For the given code, analyze:
-- What type of code comes before and after {{FILL_HERE}}
-- Whether there are existing commenting patterns to follow
-- If the code at this location represents a significant function or section that warrants documentation
-- What would be most helpful for code readability and maintenance
-
-Provide the most appropriate completion based on this analysis. Your answer must be enclosed within<COMPLETION></COMPLETION> tags and contain ONLY the code that should replace the {{FILL_HERE}} marker. Your completion will be used directly for code insertion, so it must be syntactically correct and functionally complete. Do NOT include any explanations, additional content, or repeat the entire code snippet. 
-## EXAMPLE QUERY:
-
-<QUERY>
-function sum_evens(lim) {
-  var sum = 0;
-  for (var i = 0; i < lim; ++i) {
-    {{FILL_HERE}}
-  }
-  return sum;
-}
-</QUERY>
-
-TASK: Fill the {{FILL_HERE}} hole.
-
-## CORRECT COMPLETION
-
-<COMPLETION>if (i % 2 === 0) {
-      sum += i;
-    }</COMPLETION>
-
-## EXAMPLE QUERY:
-
-<QUERY>
-def sum_list(lst):
-  total = 0
-  for x in lst:
-  {{FILL_HERE}}
-  return total
-
-print sum_list([1, 2, 3])
-</QUERY>
-
-## CORRECT COMPLETION:
-
-<COMPLETION>  total += x</COMPLETION>
-
-## EXAMPLE QUERY:
-
-<QUERY>
-// data Tree a = Node (Tree a) (Tree a) | Leaf a
-
-// sum :: Tree Int -> Int
-// sum (Node lft rgt) = sum lft + sum rgt
-// sum (Leaf val)     = val
-
-// convert to TypeScript:
-{{FILL_HERE}}
-</QUERY>
-
-## CORRECT COMPLETION:
-
-<COMPLETION>type Tree<T>
-  = {$:"Node", lft: Tree<T>, rgt: Tree<T>}
-  | {$:"Leaf", val: T};
-
-function sum(tree: Tree<number>): number {
-  switch (tree.$) {
-    case "Node":
-      return sum(tree.lft) + sum(tree.rgt);
-    case "Leaf":
-      return tree.val;
-  }
-}</COMPLETION>
-
-## EXAMPLE QUERY:
-
-The 5th {{FILL_HERE}} is Jupiter.
-
-## CORRECT COMPLETION:
-
-<COMPLETION>planet from the Sun</COMPLETION>
-
-## EXAMPLE QUERY:
-
-function hypothenuse(a, b) {
-  return Math.sqrt({{FILL_HERE}}b ** 2);
-}
-
-## CORRECT COMPLETION:
-
-<COMPLETION>a ** 2 + </COMPLETION>
+		const SYSTEM_MSG = `You are given a code snippet containing a placeholder {{FILL_HERE}}. Your task is to replace it with EXACTLY one block of content while strictly following these rules:
+1. If the placeholder appears DIRECTLY before a function/class definition, you may insert ONE language-standard comment describing ONLY that immediately following item.
+2. In other case, you should fully analyze the context and replace the placeholder with best fitting content and maintain the code's integrity and style.
+3. NEVER:
+* Add any code/comment for symbols used but not shown (they're pre-implemented)
+* Modify any existing implementation
+* Add more than one content block
+* Include implementation details in docstrings
+4. When uncertain, default to the most minimal compliant option (empty line).
 `
 		return SYSTEM_MSG
 	},
