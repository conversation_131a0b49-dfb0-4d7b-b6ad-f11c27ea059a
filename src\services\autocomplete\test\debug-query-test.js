/**
 * Debug test to identify query binding errors
 */

console.log("🔍 Starting Query Binding Debug Test...")

async function testQueryBindings() {
	try {
		// Import the compiled language parser module
		const path = require("path")
		const modulePath = path.join(process.cwd(), "out/services/tree-sitter/languageParser")
		const { getImportQuery } = require(modulePath)

		const testFiles = [
			"test.js", // JavaScript
			"test.ts", // TypeScript
			"test.py", // Python
			"test.rs", // Rust
			"test.go", // Go
			"test.java", // Java
			"test.cpp", // C++
			"test.c", // C
			"test.cs", // C#
			"test.rb", // Ruby
			"test.php", // PHP
			"test.swift", // Swift
			"test.kt", // Kotlin
		]

		console.log("\n📋 Testing Import Query Loading for All Languages...")
		console.log("=".repeat(60))

		let successCount = 0
		let errorCount = 0
		const errors = []

		for (const filename of testFiles) {
			try {
				console.log(`\n🧪 Testing ${filename}...`)

				const query = await getImportQuery(filename)

				if (query) {
					console.log(`  ✅ ${filename}: Query loaded successfully`)
					successCount++
				} else {
					console.log(`  ⚠️  ${filename}: No query available (language not supported)`)
				}
			} catch (error) {
				console.log(`  ❌ ${filename}: ERROR - ${error.message}`)
				console.log(`     Stack: ${error.stack}`)
				errors.push({ file: filename, error: error.message, stack: error.stack })
				errorCount++
			}
		}

		console.log("\n" + "=".repeat(60))
		console.log("📊 QUERY BINDING TEST RESULTS")
		console.log("=".repeat(60))

		console.log(`✅ Successful: ${successCount}/${testFiles.length}`)
		console.log(`❌ Errors: ${errorCount}/${testFiles.length}`)

		if (errors.length > 0) {
			console.log("\n🚨 DETAILED ERROR ANALYSIS:")
			console.log("-".repeat(60))

			errors.forEach((err, index) => {
				console.log(`\n${index + 1}. ${err.file}:`)
				console.log(`   Error: ${err.error}`)
				console.log(`   Stack: ${err.stack.split("\n")[0]}`)
			})

			// Group errors by type
			const errorTypes = {}
			errors.forEach((err) => {
				const errorType = err.error.split(":")[0] || "Unknown"
				if (!errorTypes[errorType]) {
					errorTypes[errorType] = []
				}
				errorTypes[errorType].push(err.file)
			})

			console.log("\n📈 ERROR PATTERNS:")
			console.log("-".repeat(40))
			Object.entries(errorTypes).forEach(([type, files]) => {
				console.log(`${type}: ${files.join(", ")}`)
			})
		}

		console.log("\n" + "=".repeat(60))
		console.log("🏁 DEBUG TEST COMPLETED")
		console.log("=".repeat(60))

		return { successCount, errorCount, errors }
	} catch (error) {
		console.error("💥 Fatal error in debug test:", error)
		console.error("Stack:", error.stack)
		return { successCount: 0, errorCount: 1, errors: [{ file: "FATAL", error: error.message, stack: error.stack }] }
	}
}

// Run the test
testQueryBindings()
	.then((results) => {
		if (results.errorCount > 0) {
			console.log(`\n🔧 NEXT STEPS: Fix ${results.errorCount} query binding errors`)
			process.exit(1)
		} else {
			console.log("\n🎉 All query bindings working correctly!")
			process.exit(0)
		}
	})
	.catch((error) => {
		console.error("💥 Unhandled error:", error)
		process.exit(1)
	})
