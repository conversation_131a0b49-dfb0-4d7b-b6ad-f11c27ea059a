import React from "react"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import styled from "styled-components"

const IconButtonContainer = styled.div`
	display: flex;
	align-items: center;
`

interface IconButtonProps {
	iconClass: string
	title: string
	disabled?: boolean
	isLoading?: boolean
	onClick: () => void
}

const IconButton: React.FC<IconButtonProps> = ({ iconClass, title, disabled, isLoading, onClick }) => {
	return (
		<IconButtonContainer>
			<VSCodeButton
				appearance="icon"
				aria-label={title}
				disabled={disabled || isLoading}
				onClick={onClick}
				style={{ padding: "0px", height: "20px" }}>
				<span className={`codicon ${iconClass} flex items-center`} style={{ fontSize: "14px", marginBottom: -3 }} />
			</VSCodeButton>
		</IconButtonContainer>
	)
}

export default IconButton
