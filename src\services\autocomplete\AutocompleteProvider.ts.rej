diff a/src/services/autocomplete/AutocompleteProvider.ts b/src/services/autocomplete/AutocompleteProvider.ts	(rejected hunks)
@@ -83,6 +83,7 @@ function setupAutocomplete(context: vscode.ExtensionContext): vscode.Disposable
 	let activeRequestId: string | null = null
 	let shouldSkipAutocomplete = false // Flag to track when autocomplete should be skipped
 	let justAcceptedSuggestion = false // Flag to track if a suggestion was just accepted
+	let justProvidedCompletion = false // Flag to track if we just provided a completion
 	let totalCompletionCalls = 0 // Track the total number of completion calls
 	let totalAcceptedSuggestions = 0 // Track the total number of accepted suggestions
 
@@ -126,7 +127,9 @@ function setupAutocomplete(context: vscode.ExtensionContext): vscode.Disposable
 		animationManager.stopAnimation()
 
 		shouldSkipAutocomplete = false
-		justAcceptedSuggestion = false
+		// Don't reset justAcceptedSuggestion here - let the timer handle it
+		// justAcceptedSuggestion = false
+		// Don't reset justProvidedCompletion here - let the document handler use it first
 		activeRequestId = null
 	}
 
@@ -266,7 +267,7 @@ function setupAutocomplete(context: vscode.ExtensionContext): vscode.Disposable
 				if (chunk.type === "text") {
 					completion += chunk.text
 					processedCompletion = processModelResponse(completion)
-					lineCount += processedCompletion.split("/n").length
+					lineCount += processedCompletion.split("\n").length
 				} else if (chunk.type === "usage") {
 					completionCost = chunk.totalCost ?? 0
 				}
@@ -363,8 +364,8 @@ function setupAutocomplete(context: vscode.ExtensionContext): vscode.Disposable
 				return null
 			}
 
-			// Get exactly what's been typed on the current line
-			const linePrefix = document.getText(new vscode.Range(new vscode.Position(position.line, 0), position)).trimStart()
+			// Get exactly what's been typed on the current line (preserve leading whitespace)
+			const linePrefix = document.getText(new vscode.Range(new vscode.Position(position.line, 0), position))
 			console.log(`🚀🛑 Autocomplete for line with prefix: "${linePrefix}"!`)
 
 			const codeContext = await contextGatherer.gatherContext(document, position, true, true)
@@ -403,6 +404,19 @@ function setupAutocomplete(context: vscode.ExtensionContext): vscode.Disposable
 				processedCompletion,
 			})
 
+			// Use the model's returned content with correct indentation
+			// If the completion starts with whitespace, it means the model provided the correct indentation
+			// We should replace the entire line prefix with the model's suggestion
+			let finalCompletion = processedCompletion
+			let insertRange = new vscode.Range(position, position) // Default: insert at cursor
+
+			if (processedCompletion && processedCompletion.match(/^\s/)) {
+				// Model returned content with leading whitespace (correct indentation)
+				// Replace from beginning of line to cursor position
+				insertRange = new vscode.Range(new vscode.Position(position.line, 0), position)
+				console.log(`🚀🔧 Using model's indentation, replacing line prefix: "${linePrefix}"`)
+			}
+
 			// Cache the successful completion for future use
 			if (processedCompletion) {
 				const completions = completionsCache.get(cacheKey) ?? []
@@ -421,7 +435,10 @@ function setupAutocomplete(context: vscode.ExtensionContext): vscode.Disposable
 				completionsCache.set(cacheKey, completions)
 			}
 
-			return [createInlineCompletionItem(processedCompletion, position)]
+			const completionItem = createInlineCompletionItem(finalCompletion, position, insertRange)
+			console.log("🚀📤 Returning completion item with command:", completionItem.command)
+			justProvidedCompletion = true // Mark that we just provided a completion
+			return [completionItem]
 		},
 	}
 
@@ -473,21 +490,29 @@ Model: ${settings.modelId || DEFAULT_MODEL}\
 
 	// Command to track when a suggestion is accepted
 	let acceptCompletionTimer: NodeJS.Timeout | null = null
-	const trackAcceptedSuggestionCommand = vscode.commands.registerCommand("qax-code.trackAcceptedSuggestion", () => {
+	let shouldTriggerNextCompletion = false
+	const trackAcceptedSuggestionCommand = vscode.commands.registerCommand("qax-code.trackAcceptedSuggestion", (...args) => {
+		console.log("🚀🎯 trackAcceptedSuggestion command called with args:", args)
 		justAcceptedSuggestion = true
 		totalAcceptedSuggestions++
+		shouldTriggerNextCompletion = true
 		console.log(`🚀✅ Suggestion accepted (${totalAcceptedSuggestions} total accepted)`)
 		updateStatusBar()
 
-		// Set a timer to re-enable completions if no further typing occurs
+		// Immediately trigger next completion after a very short delay
+		setTimeout(() => {
+			justAcceptedSuggestion = false
+			vscode.commands.executeCommand("editor.action.inlineSuggest.trigger")
+			console.log("🚀🔄 Immediately triggering next autocomplete")
+		}, 50)
+
+		// Set a timer to re-enable completions
 		if (acceptCompletionTimer) {
 			clearTimeout(acceptCompletionTimer)
 		}
 		acceptCompletionTimer = setTimeout(() => {
-			justAcceptedSuggestion = false
-			vscode.commands.executeCommand("editor.action.inlineSuggest.trigger")
-			console.log("🚀🔄 Re-triggering autocomplete after acceptance")
-		}, 1000) // 1000ms delay before re-triggering
+			acceptCompletionTimer = null // Clear the timer reference
+		}, 100) // Very short delay just to let the acceptance complete
 	})
 
 	// Event handlers
@@ -504,23 +529,48 @@ Model: ${settings.modelId || DEFAULT_MODEL}\
 
 		clearState()
 
-		// Consolidated trigger condition checks using EditDetectionUtils
-		// This includes comprehensive detection for:
-		// - Non-human edits (AI tools, extensions, etc.)
-		// - Copy-paste operations (multi-line or large insertions)
-		// - Backspace/delete operations
-		// - Code structure patterns that suggest AI generation
-		const isHumanTyping = isHumanEdit(e)
-		if (!isHumanTyping) {
-			shouldSkipAutocomplete = true
-			console.log("🚀🤖 Skipping autocomplete trigger - non-human edit detected")
-			return
+		// Check if this might be a completion acceptance
+		const mightBeCompletionAcceptance = justProvidedCompletion
+		if (mightBeCompletionAcceptance) {
+			console.log("🚀🎯 Detected potential completion acceptance, skipping human edit check")
+			justProvidedCompletion = false // Reset the flag
+			// This is likely a completion acceptance, treat it as human input
+		} else {
+			// Consolidated trigger condition checks using EditDetectionUtils
+			// This includes comprehensive detection for:
+			// - Non-human edits (AI tools, extensions, etc.)
+			// - Copy-paste operations (multi-line or large insertions)
+			// - Backspace/delete operations
+			// - Code structure patterns that suggest AI generation
+			const isHumanTyping = isHumanEdit(e)
+			if (!isHumanTyping) {
+				shouldSkipAutocomplete = true
+				console.log("🚀🤖 Skipping autocomplete trigger - non-human edit detected")
+				return
+			}
 		}
 
-		// Reset flags when the user makes any valid edit
-		justAcceptedSuggestion = false
+		// Reset shouldSkipAutocomplete flag when the user makes any valid edit
 		shouldSkipAutocomplete = false
 
+		// Handle post-acceptance triggering
+		if (shouldTriggerNextCompletion) {
+			shouldTriggerNextCompletion = false
+			// This is the first edit after accepting a completion
+			// Trigger autocomplete immediately for the next suggestion
+			setTimeout(() => {
+				vscode.commands.executeCommand("editor.action.inlineSuggest.trigger")
+				console.log("🚀🔄 Triggered next autocomplete after acceptance")
+			}, 50) // Very short delay to ensure the edit is processed
+			return
+		}
+
+		// Only reset justAcceptedSuggestion if this is a new user input (not from accepting completion)
+		// We detect this by checking if the timer is still active
+		if (!acceptCompletionTimer) {
+			justAcceptedSuggestion = false
+		}
+
 		// Force inlineSuggestions to appear, even for whitespace changes
 		// without this, hitting keys like spacebar won't show the completion
 		vscode.commands.executeCommand("editor.action.inlineSuggest.trigger")
@@ -553,10 +603,14 @@ Model: ${settings.modelId || DEFAULT_MODEL}\
  * @param position The position in the document
  * @returns A configured vscode.InlineCompletionItem
  */
-function createInlineCompletionItem(completionText: string, position: vscode.Position): vscode.InlineCompletionItem {
-	const insertRange = new vscode.Range(position, position)
-
-	return Object.assign(new vscode.InlineCompletionItem(completionText, insertRange), {
+function createInlineCompletionItem(
+	completionText: string,
+	position: vscode.Position,
+	insertRange?: vscode.Range,
+): vscode.InlineCompletionItem {
+	const range = insertRange || new vscode.Range(position, position)
+
+	return Object.assign(new vscode.InlineCompletionItem(completionText, range), {
 		command: {
 			command: "qax-code.trackAcceptedSuggestion",
 			title: "Track Accepted Suggestion",
