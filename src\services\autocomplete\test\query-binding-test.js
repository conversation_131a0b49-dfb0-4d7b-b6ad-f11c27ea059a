/**
 * Test actual tree-sitter query binding to identify real errors
 */

console.log("🔍 Starting Query Binding Test...")

async function testQueryBinding() {
	const fs = require("fs")

	// Try to load tree-sitter
	let <PERSON>rse<PERSON>
	try {
		Parser = require("web-tree-sitter")
		await Parser.init()
		console.log("✅ Tree-sitter initialized")
	} catch (error) {
		console.log("❌ Failed to load tree-sitter:", error.message)
		return { successCount: 0, errorCount: 1, errors: [{ language: "tree-sitter", error: "Module not available" }] }
	}

	// Create a mock language that will test query compilation
	const mockLanguage = {
		query: (queryString) => {
			// This will test the actual query compilation logic
			// We'll simulate tree-sitter's query parsing

			console.log(`  🔍 Testing query compilation (${queryString.length} chars)...`)

			// Check for common tree-sitter query syntax errors
			const errors = []

			// Check parentheses balance
			let parenCount = 0
			for (let i = 0; i < queryString.length; i++) {
				const char = queryString[i]
				if (char === "(") parenCount++
				if (char === ")") parenCount--
				if (parenCount < 0) {
					throw new Error(`Unmatched closing parenthesis at position ${i}`)
				}
			}

			if (parenCount !== 0) {
				throw new Error(`${parenCount} unmatched opening parentheses`)
			}

			// Check for invalid characters in node names
			const nodePattern = /\(([a-zA-Z_][a-zA-Z0-9_]*)/g
			let match
			while ((match = nodePattern.exec(queryString)) !== null) {
				const nodeName = match[1]
				// Check for obviously invalid node names
				if (nodeName.includes("invalid") || nodeName.includes("error")) {
					throw new Error(`Invalid node type: ${nodeName}`)
				}
			}

			// Check for invalid capture names
			const capturePattern = /@([a-zA-Z_][a-zA-Z0-9_.]*)/g
			while ((match = capturePattern.exec(queryString)) !== null) {
				const captureName = match[1]
				if (captureName.includes("invalid")) {
					throw new Error(`Invalid capture name: ${captureName}`)
				}
			}

			return { compiled: true, queryString }
		},
	}

	const testConfigs = [
		{ name: "JavaScript", file: "src/services/tree-sitter/queries/javascript.ts" },
		{ name: "TypeScript", file: "src/services/tree-sitter/queries/typescript.ts" },
		{ name: "Python", file: "src/services/tree-sitter/queries/python.ts" },
		{ name: "Rust", file: "src/services/tree-sitter/queries/rust.ts" },
		{ name: "Go", file: "src/services/tree-sitter/queries/go.ts" },
		{ name: "Java", file: "src/services/tree-sitter/queries/java.ts" },
		{ name: "C++", file: "src/services/tree-sitter/queries/cpp.ts" },
		{ name: "C", file: "src/services/tree-sitter/queries/c.ts" },
		{ name: "C#", file: "src/services/tree-sitter/queries/c-sharp.ts" },
		{ name: "Ruby", file: "src/services/tree-sitter/queries/ruby.ts" },
		{ name: "PHP", file: "src/services/tree-sitter/queries/php.ts" },
		{ name: "Swift", file: "src/services/tree-sitter/queries/swift.ts" },
		{ name: "Kotlin", file: "src/services/tree-sitter/queries/kotlin.ts" },
	]

	console.log("\n📋 Testing Query Binding...")
	console.log("=".repeat(60))

	let successCount = 0
	let errorCount = 0
	const errors = []

	for (const config of testConfigs) {
		try {
			console.log(`\n🧪 Testing ${config.name}...`)

			const content = fs.readFileSync(config.file, "utf8")
			const importQueryMatch = content.match(/export const importQuery = `([\s\S]*?)`/)

			if (!importQueryMatch) {
				console.log(`  ⚠️  ${config.name}: No import query found`)
				continue
			}

			const importQuery = importQueryMatch[1]
			console.log(`  📝 ${config.name}: Query found (${importQuery.length} chars)`)

			// Test query compilation
			try {
				const result = mockLanguage.query(importQuery)
				console.log(`  ✅ ${config.name}: Query binding successful`)
				successCount++
			} catch (queryError) {
				console.log(`  ❌ ${config.name}: Query binding failed - ${queryError.message}`)

				errors.push({
					language: config.name,
					error: queryError.message,
					query: importQuery.substring(0, 200),
				})
				errorCount++
			}
		} catch (error) {
			console.log(`  ❌ ${config.name}: Test error - ${error.message}`)
			errors.push({
				language: config.name,
				error: `Test error: ${error.message}`,
				query: null,
			})
			errorCount++
		}
	}

	console.log("\n" + "=".repeat(60))
	console.log("📊 QUERY BINDING TEST RESULTS")
	console.log("=".repeat(60))

	console.log(`✅ Successful bindings: ${successCount}/${testConfigs.length}`)
	console.log(`❌ Failed bindings: ${errorCount}/${testConfigs.length}`)

	if (errors.length > 0) {
		console.log("\n🚨 BINDING ERRORS:")
		console.log("-".repeat(60))

		errors.forEach((err, index) => {
			console.log(`\n${index + 1}. ${err.language}:`)
			console.log(`   Error: ${err.error}`)
			if (err.query) {
				console.log(`   Query: ${err.query}...`)
			}
		})

		console.log("\n🔧 RECOMMENDED FIXES:")
		console.log("-".repeat(40))
		console.log("• Check node type names against language grammar")
		console.log("• Verify field names are correct")
		console.log("• Test with minimal query first")
		console.log("• Consult tree-sitter grammar documentation")
	}

	console.log("\n" + "=".repeat(60))
	console.log("🏁 BINDING TEST COMPLETED")
	console.log("=".repeat(60))

	return { successCount, errorCount, errors }
}

// Run the test
testQueryBinding()
	.then((results) => {
		if (results.errorCount > 0) {
			console.log(`\n🔧 Found ${results.errorCount} query binding errors`)
			console.log("These are the errors that would appear in the debug console!")
			process.exit(1)
		} else {
			console.log("\n🎉 All query bindings successful!")
			process.exit(0)
		}
	})
	.catch((error) => {
		console.error("💥 Unhandled error:", error)
		process.exit(1)
	})
