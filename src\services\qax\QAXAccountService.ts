/**
 * <AUTHOR>
 */

import { QAXUserInfo } from "@shared/QAXAccount"
import * as vscode from "vscode"
import { QAXConfig } from "../../utils/qax-config"

export class QAXAccountService {
	private context: vscode.ExtensionContext

	constructor(context: vscode.ExtensionContext) {
		this.context = context
	}

	// JWT Token 管理
	async storeJWTToken(token: string): Promise<void> {
		await this.context.secrets.store("qaxCodegenToken", token)
	}

	async getJWTToken(): Promise<string | undefined> {
		return await this.context.secrets.get("qaxCodegenToken")
	}

	async clearJWTToken(): Promise<void> {
		await this.context.secrets.delete("qaxCodegenToken")
	}

	// 用户信息提取
	extractUserInfoFromToken(token: string): QAXUserInfo | null {
		const payload = QAXConfig.decodeJWTToken(token)
		if (!payload) {
			return null
		}

		return {
			userId: payload.sub,
			username: payload.name,
			email: payload.email,
			displayName: payload.display_name,
			employeeNumber: payload.employee_number,
			lastLoginAt: payload.iat * 1000,
		}
	}

	// 认证状态管理
	async isAuthenticated(): Promise<boolean> {
		const token = await this.getJWTToken()
		if (!token) {
			return false
		}
		return QAXConfig.isValidJWTToken(token)
	}

	async getCurrentUser(): Promise<QAXUserInfo | null> {
		const token = await this.getJWTToken()
		if (!token) {
			return null
		}
		return this.extractUserInfoFromToken(token)
	}

	// 认证流程
	getAuthUrl(ref: string): string {
		return QAXConfig.getCodegenAuthUrl(ref)
	}

	async handleAuthCallback(jwtToken: string): Promise<QAXUserInfo | null> {
		if (!QAXConfig.isValidJWTToken(jwtToken)) {
			return null
		}

		await this.storeJWTToken(jwtToken)
		return this.extractUserInfoFromToken(jwtToken)
	}

	async logout(): Promise<boolean> {
		try {
			await this.clearJWTToken()
			return true
		} catch {
			return false
		}
	}
}
