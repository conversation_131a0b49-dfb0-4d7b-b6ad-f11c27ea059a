/**
 * Standalone test runner for ContextGatherer functionality
 * This file can be run directly with Node.js to test core functionality
 */

console.log("🧪 Starting ContextGatherer Standalone Tests...")
console.log("=".repeat(80))

// Test 1: Import Query System Architecture
async function testImportQueryArchitecture() {
	console.log("\n📦 Test 1: Import Query System Architecture")
	console.log("-".repeat(50))

	try {
		// Test that import queries are properly integrated into existing query files
		const fs = require("fs")
		const path = require("path")

		const queryFiles = [
			"src/services/tree-sitter/queries/javascript.ts",
			"src/services/tree-sitter/queries/typescript.ts",
			"src/services/tree-sitter/queries/python.ts",
			"src/services/tree-sitter/queries/rust.ts",
			"src/services/tree-sitter/queries/go.ts",
			"src/services/tree-sitter/queries/java.ts",
			"src/services/tree-sitter/queries/cpp.ts",
			"src/services/tree-sitter/queries/c.ts",
			"src/services/tree-sitter/queries/c-sharp.ts",
			"src/services/tree-sitter/queries/ruby.ts",
			"src/services/tree-sitter/queries/php.ts",
			"src/services/tree-sitter/queries/swift.ts",
			"src/services/tree-sitter/queries/kotlin.ts",
		]

		let queriesWithImports = 0
		let totalQueries = 0

		for (const file of queryFiles) {
			try {
				const content = fs.readFileSync(file, "utf8")
				totalQueries++

				// Check if file has both definition and import queries
				const hasDefinitionQuery = content.includes("export const definitionQuery")
				const hasImportQuery = content.includes("export const importQuery")
				const hasDefaultExport = content.includes("export default definitionQuery")

				if (hasDefinitionQuery && hasImportQuery && hasDefaultExport) {
					queriesWithImports++
					console.log(`  ✅ ${path.basename(file)}: Has both definition and import queries`)
				} else {
					console.log(
						`  ❌ ${path.basename(file)}: Missing queries - def:${hasDefinitionQuery}, import:${hasImportQuery}, default:${hasDefaultExport}`,
					)
				}
			} catch (error) {
				console.log(`  ❌ ${path.basename(file)}: Error reading file - ${error.message}`)
			}
		}

		const success = queriesWithImports >= 10 // Most languages should work
		console.log(`\n  📊 Result: ${queriesWithImports}/${totalQueries} query files properly structured`)
		console.log(`  ${success ? "✅" : "❌"} Architecture test: ${success ? "PASSED" : "FAILED"}`)

		return {
			name: "Import Query Architecture",
			passed: success,
			details: `${queriesWithImports}/${totalQueries} files structured correctly`,
		}
	} catch (error) {
		console.log(`  ❌ Error: ${error.message}`)
		return { name: "Import Query Architecture", passed: false, details: `Error: ${error.message}` }
	}
}

// Test 2: ContextGatherer Integration
async function testContextGathererIntegration() {
	console.log("\n🔧 Test 2: ContextGatherer Integration")
	console.log("-".repeat(50))

	try {
		const fs = require("fs")
		const contextGathererPath = "src/services/autocomplete/ContextGatherer.ts"
		const content = fs.readFileSync(contextGathererPath, "utf8")

		// Check for key integration points
		const checks = {
			"Import Query Import": content.includes("getImportQuery"),
			"Tree-sitter Import Method": content.includes("extractImportedSymbolsWithTreeSitter"),
			"Recently Visited Functions": content.includes("getRecentlyVisitedFunctionContext"),
			"Clipboard Integration": content.includes("getClipboardContent"),
			"Function Signature Extraction": content.includes("_getFunctionSignature"),
			"Comment Inclusion": content.includes("_includeCommentsForNode"),
			"Deduplication Logic": content.includes("_deduplicateFunctionDefinitions"),
		}

		let passedChecks = 0
		const totalChecks = Object.keys(checks).length

		for (const [checkName, passed] of Object.entries(checks)) {
			if (passed) {
				passedChecks++
				console.log(`  ✅ ${checkName}: Found`)
			} else {
				console.log(`  ❌ ${checkName}: Missing`)
			}
		}

		// Check CodeContext interface
		const hasClipboardField = content.includes("clipboardContent?:")
		console.log(
			`  ${hasClipboardField ? "✅" : "❌"} CodeContext Interface: ${hasClipboardField ? "Updated with clipboard field" : "Missing clipboard field"}`,
		)

		const success = passedChecks >= 5 && hasClipboardField
		console.log(`\n  📊 Result: ${passedChecks}/${totalChecks} integration points found`)
		console.log(`  ${success ? "✅" : "❌"} Integration test: ${success ? "PASSED" : "FAILED"}`)

		return {
			name: "ContextGatherer Integration",
			passed: success,
			details: `${passedChecks}/${totalChecks} integration points + interface update`,
		}
	} catch (error) {
		console.log(`  ❌ Error: ${error.message}`)
		return { name: "ContextGatherer Integration", passed: false, details: `Error: ${error.message}` }
	}
}

// Test 3: Language Parser Integration
async function testLanguageParserIntegration() {
	console.log("\n🌳 Test 3: Language Parser Integration")
	console.log("-".repeat(50))

	try {
		const fs = require("fs")
		const languageParserPath = "src/services/tree-sitter/languageParser.ts"
		const content = fs.readFileSync(languageParserPath, "utf8")

		// Check for import query integration
		const checks = {
			"Import Query Imports": content.includes("import { importQuery as"),
			"getImportQueryForLanguage Function": content.includes("getImportQueryForLanguage"),
			"getImportQuery Export": content.includes("export async function getImportQuery"),
			"Language Name Helper": content.includes("getLanguageNameFromFilepath"),
			"Multiple Language Support": content.includes("javascriptImportQuery") && content.includes("pythonImportQuery"),
		}

		let passedChecks = 0
		const totalChecks = Object.keys(checks).length

		for (const [checkName, passed] of Object.entries(checks)) {
			if (passed) {
				passedChecks++
				console.log(`  ✅ ${checkName}: Found`)
			} else {
				console.log(`  ❌ ${checkName}: Missing`)
			}
		}

		const success = passedChecks >= 4
		console.log(`\n  📊 Result: ${passedChecks}/${totalChecks} integration points found`)
		console.log(`  ${success ? "✅" : "❌"} Language Parser test: ${success ? "PASSED" : "FAILED"}`)

		return {
			name: "Language Parser Integration",
			passed: success,
			details: `${passedChecks}/${totalChecks} integration points found`,
		}
	} catch (error) {
		console.log(`  ❌ Error: ${error.message}`)
		return { name: "Language Parser Integration", passed: false, details: `Error: ${error.message}` }
	}
}

// Test 4: Test Infrastructure
async function testTestInfrastructure() {
	console.log("\n🧪 Test 4: Test Infrastructure")
	console.log("-".repeat(50))

	try {
		const fs = require("fs")

		const testFiles = [
			"src/services/autocomplete/test/SimpleContextTest.ts",
			"src/services/autocomplete/test/runTests.ts",
			"src/services/autocomplete/test/README.md",
			"src/services/autocomplete/test/COMPREHENSIVE_TEST_SUMMARY.md",
		]

		let existingFiles = 0
		const totalFiles = testFiles.length

		for (const file of testFiles) {
			try {
				fs.accessSync(file)
				existingFiles++
				console.log(`  ✅ ${file.split("/").pop()}: Exists`)
			} catch (error) {
				console.log(`  ❌ ${file.split("/").pop()}: Missing`)
			}
		}

		// Check extension.ts integration
		const extensionPath = "src/extension.ts"
		const extensionContent = fs.readFileSync(extensionPath, "utf8")
		const hasTestIntegration = extensionContent.includes("runSimpleContextTests")

		console.log(`  ${hasTestIntegration ? "✅" : "❌"} Extension Integration: ${hasTestIntegration ? "Found" : "Missing"}`)

		const success = existingFiles >= 3 && hasTestIntegration
		console.log(`\n  📊 Result: ${existingFiles}/${totalFiles} test files + extension integration`)
		console.log(`  ${success ? "✅" : "❌"} Test Infrastructure: ${success ? "PASSED" : "FAILED"}`)

		return {
			name: "Test Infrastructure",
			passed: success,
			details: `${existingFiles}/${totalFiles} files + extension integration`,
		}
	} catch (error) {
		console.log(`  ❌ Error: ${error.message}`)
		return { name: "Test Infrastructure", passed: false, details: `Error: ${error.message}` }
	}
}

// Main test runner
async function runAllTests() {
	const tests = [
		testImportQueryArchitecture,
		testContextGathererIntegration,
		testLanguageParserIntegration,
		testTestInfrastructure,
	]

	const results = []

	for (const test of tests) {
		try {
			const result = await test()
			results.push(result)
		} catch (error) {
			results.push({
				name: test.name || "Unknown Test",
				passed: false,
				details: `Error: ${error.message}`,
			})
		}
	}

	// Print summary
	console.log("\n" + "=".repeat(80))
	console.log("🧪 STANDALONE TEST RESULTS")
	console.log("=".repeat(80))

	const passed = results.filter((r) => r.passed).length
	const total = results.length
	const passRate = ((passed / total) * 100).toFixed(1)

	console.log(`\n📊 SUMMARY: ${passed}/${total} tests passed (${passRate}%)`)
	console.log(`${passed === total ? "🎉 ALL TESTS PASSED!" : "⚠️  Some tests failed"}`)

	console.log("\n📋 DETAILED RESULTS:")
	console.log("-".repeat(80))

	results.forEach((result) => {
		const status = result.passed ? "✅" : "❌"
		console.log(`${status} ${result.name}`)
		console.log(`   ${result.details}`)
	})

	console.log("\n" + "=".repeat(80))
	console.log("🏁 STANDALONE TESTS COMPLETED")
	console.log("=".repeat(80))

	return results
}

// Run tests
runAllTests().catch((error) => {
	console.error("Fatal error running tests:", error)
	process.exit(1)
})
