import { StateServiceClient } from "@/services/grpc-client"
import { TelemetrySettingEnum, TelemetrySettingRequest } from "@shared/proto/state"
import { memo } from "react"
import styled from "styled-components"

const BannerContainer = styled.div`
	background-color: var(--vscode-banner-background);
	padding: 12px 20px;
	display: flex;
	flex-direction: column;
	gap: 10px;
	flex-shrink: 0;
	margin-bottom: 6px;
	position: relative;
`

const CloseButton = styled.button`
	position: absolute;
	top: 12px;
	right: 12px;
	background: none;
	border: none;
	color: var(--vscode-foreground);
	cursor: pointer;
	font-size: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 4px;
	opacity: 0.7;
	&:hover {
		opacity: 1;
	}
`

const ButtonContainer = styled.div`
	display: flex;
	gap: 8px;
	width: 100%;

	& > vscode-button {
		flex: 1;
	}
`

const TelemetryBanner = () => {
	const handleClose = async () => {
		try {
			await StateServiceClient.updateTelemetrySetting(
				TelemetrySettingRequest.create({
					setting: TelemetrySettingEnum.ENABLED,
				}),
			)
		} catch (error) {
			console.error("Error updating telemetry setting:", error)
		}
	}

	return (
		<BannerContainer>
			<CloseButton onClick={handleClose} aria-label="Close banner and enable telemetry">
				✕
			</CloseButton>
			<div>
				<strong>帮助改进 QAXCodegen</strong>
				<i>
					<br />
					（并获得实验性功能访问权限）
				</i>
				<div style={{ marginTop: 4 }}>
					QAXCodegen 是您的智能编程助手，可以帮助您：创建和编辑文件、理解复杂项目结构、执行终端命令、浏览网页内容。
					<div style={{ marginTop: 4 }}>开始对话，体验 AI 驱动的代码生成和项目管理能力。</div>
				</div>
			</div>
		</BannerContainer>
	)
}

export default memo(TelemetryBanner)
