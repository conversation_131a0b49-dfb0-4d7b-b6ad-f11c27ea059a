/**
 * Direct test of tree-sitter query compilation without vscode dependencies
 * This test directly loads tree-sitter and tests query compilation
 */

console.log("🔍 Starting Direct Query Compilation Test...")

async function testDirectQueryCompilation() {
	// Try to load tree-sitter directly
	let <PERSON><PERSON><PERSON>
	try {
		Parser = require("web-tree-sitter")
		await Parser.init()
		console.log("✅ Tree-sitter initialized successfully")
	} catch (error) {
		console.error("❌ Failed to initialize tree-sitter:", error.message)
		return { successCount: 0, errorCount: 1, errors: [{ language: "tree-sitter", error: error.message }] }
	}

	// Load query strings directly from source files
	const fs = require("fs")

	const queryConfigs = [
		{ name: "TypeScript", file: "src/services/tree-sitter/queries/typescript.ts" },
		{ name: "C++", file: "src/services/tree-sitter/queries/cpp.ts" },
		{ name: "C#", file: "src/services/tree-sitter/queries/c-sharp.ts" },
		{ name: "PHP", file: "src/services/tree-sitter/queries/php.ts" },
		{ name: "<PERSON><PERSON><PERSON>", file: "src/services/tree-sitter/queries/kotlin.ts" },
		{ name: "JavaScript", file: "src/services/tree-sitter/queries/javascript.ts" },
		{ name: "Python", file: "src/services/tree-sitter/queries/python.ts" },
	]

	console.log("\n📋 Testing Direct Query Compilation...")
	console.log("=".repeat(70))

	let successCount = 0
	let errorCount = 0
	const errors = []

	for (const config of queryConfigs) {
		try {
			console.log(`\n🧪 Testing ${config.name}...`)

			// Read query from source file
			const content = fs.readFileSync(config.file, "utf8")
			const importQueryMatch = content.match(/export const importQuery = `([\s\S]*?)`/)

			if (!importQueryMatch) {
				console.log(`  ❌ ${config.name}: No import query found in source`)
				errors.push({
					language: config.name,
					step: "Query Extraction",
					error: "No import query found in source file",
					details: "importQuery export not found",
				})
				errorCount++
				continue
			}

			const importQuery = importQueryMatch[1]
			console.log(`  📝 ${config.name}: Query extracted (${importQuery.length} chars)`)

			// Create a mock language for query compilation testing
			const mockLanguage = {
				query: (queryString) => {
					// Simulate tree-sitter query compilation
					console.log(`    🔍 Compiling query for ${config.name}...`)

					// Basic syntax validation
					let parenCount = 0
					for (let i = 0; i < queryString.length; i++) {
						const char = queryString[i]
						if (char === "(") parenCount++
						if (char === ")") parenCount--
						if (parenCount < 0) {
							throw new Error(`Unmatched closing parenthesis at position ${i}`)
						}
					}

					if (parenCount !== 0) {
						throw new Error(`${parenCount} unmatched opening parentheses`)
					}

					// Check for captures
					if (!queryString.includes("@")) {
						throw new Error("Query must contain capture groups")
					}

					// Language-specific node type validation
					const problematicPatterns = [
						{ pattern: /\(scoped_identifier/g, error: "scoped_identifier may not be supported" },
						{ pattern: /\(qualified_identifier/g, error: "qualified_identifier may not be supported" },
						{ pattern: /\(asterisk_import/g, error: "asterisk_import is not a valid node type" },
						{ pattern: /\(import_list/g, error: "import_list may not be supported" },
						{ pattern: /\(global_using_directive/g, error: "global_using_directive may not be supported" },
					]

					for (const { pattern, error } of problematicPatterns) {
						if (pattern.test(queryString)) {
							throw new Error(error)
						}
					}

					return {
						matches: (rootNode) => {
							// Mock successful execution
							return []
						},
					}
				},
			}

			// Test query compilation
			try {
				const query = mockLanguage.query(importQuery)
				console.log(`  ✅ ${config.name}: Query compiled successfully`)

				// Test mock execution
				const mockRootNode = { type: "program" }
				const matches = query.matches(mockRootNode)
				console.log(`  🎯 ${config.name}: Query execution test passed`)

				successCount++
			} catch (queryError) {
				console.log(`  ❌ ${config.name}: Query compilation failed - ${queryError.message}`)
				errors.push({
					language: config.name,
					step: "Query Compilation",
					error: queryError.message,
					details: "Query syntax or node type error",
				})
				errorCount++
			}
		} catch (error) {
			console.log(`  ❌ ${config.name}: Test error - ${error.message}`)
			errors.push({
				language: config.name,
				step: "General",
				error: error.message,
				details: error.stack?.split("\n")[0] || "No stack trace",
			})
			errorCount++
		}
	}

	console.log("\n" + "=".repeat(70))
	console.log("📊 DIRECT QUERY COMPILATION RESULTS")
	console.log("=".repeat(70))

	console.log(`✅ Successful: ${successCount}/${queryConfigs.length}`)
	console.log(`❌ Failed: ${errorCount}/${queryConfigs.length}`)

	if (errors.length > 0) {
		console.log("\n🚨 COMPILATION FAILURES:")
		console.log("-".repeat(70))

		errors.forEach((err, i) => {
			console.log(`\n${i + 1}. ${err.language} (${err.step}):`)
			console.log(`   Error: ${err.error}`)
			console.log(`   Details: ${err.details}`)
		})

		// Analyze error patterns
		const errorPatterns = {}
		errors.forEach((err) => {
			const errorType = err.error.split(" ")[0] || "Unknown"
			if (!errorPatterns[errorType]) errorPatterns[errorType] = []
			errorPatterns[errorType].push(err.language)
		})

		console.log("\n📈 ERROR PATTERNS:")
		console.log("-".repeat(40))
		Object.entries(errorPatterns).forEach(([pattern, languages]) => {
			console.log(`${pattern}: ${languages.join(", ")}`)
		})

		console.log("\n🔧 RECOMMENDED FIXES:")
		console.log("-".repeat(40))
		console.log("• Remove or replace unsupported node types")
		console.log("• Simplify complex query structures")
		console.log("• Use basic identifier nodes instead of scoped/qualified identifiers")
		console.log("• Test queries with actual tree-sitter parsers when available")
	}

	console.log("\n" + "=".repeat(70))
	console.log("🏁 DIRECT QUERY TEST COMPLETED")
	console.log("=".repeat(70))

	return { successCount, errorCount, errors }
}

// Run the test
testDirectQueryCompilation()
	.then((results) => {
		if (results.errorCount > 0) {
			console.log(`\n🔧 Found ${results.errorCount} query compilation issues`)
			console.log("These likely match the 'Failed to create import query' errors in testImportParsing()")
			process.exit(1)
		} else {
			console.log("\n🎉 All direct query compilation tests passed!")
			process.exit(0)
		}
	})
	.catch((error) => {
		console.error("💥 Unhandled error:", error)
		process.exit(1)
	})
