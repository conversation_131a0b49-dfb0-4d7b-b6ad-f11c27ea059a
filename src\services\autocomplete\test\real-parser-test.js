/**
 * Test with real tree-sitter parsers to identify actual query binding errors
 */

console.log("🔍 Starting Real Parser Query Test...")

async function testRealParsers() {
	const fs = require("fs")
	const path = require("path")

	// Try to load tree-sitter
	let <PERSON><PERSON><PERSON>
	try {
		Parser = require("web-tree-sitter")
		await Parser.init()
		console.log("✅ Tree-sitter initialized")
	} catch (error) {
		console.log("❌ Failed to load tree-sitter:", error.message)
		return { successCount: 0, errorCount: 1, errors: [{ language: "tree-sitter", error: "Module not available" }] }
	}

	// Test configurations for languages we might have parsers for
	const testConfigs = [
		{
			name: "javascript",
			file: "src/services/tree-sitter/queries/javascript.ts",
			wasmPath: "tree-sitter-javascript.wasm",
			testCode: `import { foo, bar } from 'module';\nconst x = require('fs');`,
		},
		{
			name: "typescript",
			file: "src/services/tree-sitter/queries/typescript.ts",
			wasmPath: "tree-sitter-typescript.wasm",
			testCode: `import type { User } from './types';\nimport { Component } from 'react';`,
		},
		{
			name: "python",
			file: "src/services/tree-sitter/queries/python.ts",
			wasmPath: "tree-sitter-python.wasm",
			testCode: `import os\nfrom typing import List\nimport numpy as np`,
		},
		{
			name: "rust",
			file: "src/services/tree-sitter/queries/rust.ts",
			wasmPath: "tree-sitter-rust.wasm",
			testCode: `use std::collections::HashMap;\nuse serde::{Deserialize, Serialize};`,
		},
		{
			name: "go",
			file: "src/services/tree-sitter/queries/go.ts",
			wasmPath: "tree-sitter-go.wasm",
			testCode: `import "fmt"\nimport json "encoding/json"`,
		},
	]

	console.log("\n📋 Testing Real Parser Query Compilation...")
	console.log("=".repeat(60))

	let successCount = 0
	let errorCount = 0
	const errors = []

	for (const config of testConfigs) {
		try {
			console.log(`\n🧪 Testing ${config.name}...`)

			// Read query file
			const content = fs.readFileSync(config.file, "utf8")
			const importQueryMatch = content.match(/export const importQuery = `([\s\S]*?)`/)

			if (!importQueryMatch) {
				console.log(`  ⚠️  ${config.name}: No import query found`)
				continue
			}

			const importQuery = importQueryMatch[1]
			console.log(`  📝 ${config.name}: Import query found (${importQuery.length} chars)`)

			// Try to load language parser
			let language
			try {
				// Try different possible locations for WASM files
				const possiblePaths = [
					`node_modules/tree-sitter-${config.name}/tree-sitter-${config.name}.wasm`,
					`node_modules/web-tree-sitter/tree-sitter-${config.name}.wasm`,
					config.wasmPath,
				]

				let wasmPath = null
				for (const testPath of possiblePaths) {
					if (fs.existsSync(testPath)) {
						wasmPath = testPath
						break
					}
				}

				if (!wasmPath) {
					console.log(`  ⚠️  ${config.name}: WASM file not found, skipping real parser test`)
					console.log(`  📝 ${config.name}: Query syntax appears valid`)
					successCount++
					continue
				}

				language = await Parser.Language.load(wasmPath)
				console.log(`  🌳 ${config.name}: Language parser loaded`)
			} catch (loadError) {
				console.log(`  ⚠️  ${config.name}: Could not load parser (${loadError.message}), testing syntax only`)

				// Basic syntax validation
				if (importQuery.includes("@") && importQuery.includes("(") && importQuery.includes(")")) {
					console.log(`  📝 ${config.name}: Query syntax appears valid`)
					successCount++
				} else {
					console.log(`  ❌ ${config.name}: Query syntax invalid`)
					errors.push({
						language: config.name,
						error: "Invalid query syntax",
						details: "Missing captures or parentheses",
					})
					errorCount++
				}
				continue
			}

			// Test query compilation with real parser
			try {
				const query = language.query(importQuery)
				console.log(`  ✅ ${config.name}: Query compiled successfully`)

				// Test query execution
				const parser = new Parser()
				parser.setLanguage(language)
				const tree = parser.parse(config.testCode)
				const captures = query.captures(tree.rootNode)

				console.log(`  🎯 ${config.name}: Query executed, found ${captures.length} captures`)
				successCount++
			} catch (queryError) {
				console.log(`  ❌ ${config.name}: Query error - ${queryError.message}`)

				// Detailed error analysis
				const errorAnalysis = analyzeRealQueryError(importQuery, queryError.message, config.name)
				console.log(`  🔍 ${config.name}: ${errorAnalysis}`)

				errors.push({
					language: config.name,
					error: queryError.message,
					details: errorAnalysis,
					query: importQuery.substring(0, 300),
				})
				errorCount++
			}
		} catch (error) {
			console.log(`  ❌ ${config.name}: Test error - ${error.message}`)
			errors.push({
				language: config.name,
				error: `Test error: ${error.message}`,
				details: "Could not complete test",
			})
			errorCount++
		}
	}

	console.log("\n" + "=".repeat(60))
	console.log("📊 REAL PARSER TEST RESULTS")
	console.log("=".repeat(60))

	console.log(`✅ Successful: ${successCount}/${testConfigs.length}`)
	console.log(`❌ Errors: ${errorCount}/${testConfigs.length}`)

	if (errors.length > 0) {
		console.log("\n🚨 DETAILED ERROR ANALYSIS:")
		console.log("-".repeat(60))

		errors.forEach((err, index) => {
			console.log(`\n${index + 1}. ${err.language}:`)
			console.log(`   Error: ${err.error}`)
			console.log(`   Analysis: ${err.details}`)
			if (err.query) {
				console.log(`   Query: ${err.query}...`)
			}
		})

		console.log("\n🔧 COMMON FIXES:")
		console.log("-".repeat(40))
		console.log("• Check node type names match language grammar")
		console.log("• Verify field names are correct for language")
		console.log("• Ensure capture group names are valid")
		console.log("• Test with minimal query first")
	}

	console.log("\n" + "=".repeat(60))
	console.log("🏁 REAL PARSER TEST COMPLETED")
	console.log("=".repeat(60))

	return { successCount, errorCount, errors }
}

function analyzeRealQueryError(query, errorMessage, language) {
	if (errorMessage.includes("Invalid node type")) {
		return `Node type mismatch - check ${language} grammar specification`
	}
	if (errorMessage.includes("Invalid field")) {
		return `Field name error - verify ${language} node field names`
	}
	if (errorMessage.includes("syntax error")) {
		return "Query syntax error - check parentheses, quotes, and structure"
	}
	if (errorMessage.includes("capture")) {
		return "Capture group error - check @ symbols and capture names"
	}
	return `Unknown ${language} query error - check language-specific documentation`
}

/**
 * Specific test for TypeScript query binding without VSCode dependencies
 */
async function testTypeScriptImportQuery() {
	console.log("\n🎯 SPECIFIC TYPESCRIPT QUERY BINDING TEST")
	console.log("=".repeat(50))

	try {
		// Try to load tree-sitter directly
		let Parser
		try {
			Parser = require("web-tree-sitter")
			if (typeof Parser.init === "function") {
				await Parser.init()
				console.log("✅ Tree-sitter initialized")
			} else {
				console.log("⚠️  Tree-sitter init not available, continuing with syntax check only")
				Parser = null
			}
		} catch (initError) {
			console.log(`⚠️  Tree-sitter initialization failed: ${initError.message}`)
			Parser = null
		}

		// Load TypeScript query directly from source
		const fs = require("fs")
		const path = require("path")
		const queryFile = path.resolve(__dirname, "../../tree-sitter/queries/typescript.ts")
		console.log(`🔍 Loading TypeScript query from: ${queryFile}`)

		const content = fs.readFileSync(queryFile, "utf8")
		const importQueryMatch = content.match(/export const importQuery = `([\s\S]*?)`/)

		if (!importQueryMatch) {
			console.log("❌ No import query found in TypeScript query file")
			return { success: false, error: "No import query found" }
		}

		const importQuery = importQueryMatch[1]
		console.log(`📝 TypeScript import query loaded (${importQuery.length} chars)`)

		// If Parser is not available, do syntax-only validation
		if (!Parser) {
			console.log("⚠️  Parser not available, testing query syntax only")

			// Basic syntax validation
			if (
				importQuery.includes("@import.") &&
				importQuery.includes("(") &&
				importQuery.includes(")") &&
				!importQuery.includes("@_") && // No invalid capture names
				importQuery.includes("import_statement")
			) {
				console.log("✅ TypeScript query syntax appears valid")
				return { success: true, syntaxOnly: true }
			} else {
				console.log("❌ TypeScript query syntax appears invalid")
				console.log("🔍 Query preview:", importQuery.substring(0, 200) + "...")
				return { success: false, error: "Invalid query syntax" }
			}
		}

		// Try to find TypeScript parser WASM file
		const possibleWasmPaths = [
			"node_modules/tree-sitter-typescript/tree-sitter-typescript.wasm",
			"node_modules/web-tree-sitter/tree-sitter-typescript.wasm",
			"tree-sitter-typescript.wasm",
		]

		let wasmPath = null
		for (const testPath of possibleWasmPaths) {
			if (fs.existsSync(testPath)) {
				wasmPath = testPath
				break
			}
		}

		if (!wasmPath) {
			console.log("⚠️  TypeScript WASM file not found, testing query syntax only")

			// Basic syntax validation
			if (
				importQuery.includes("@import.") &&
				importQuery.includes("(") &&
				importQuery.includes(")") &&
				!importQuery.includes("@_") && // No invalid capture names
				importQuery.includes("import_statement")
			) {
				console.log("✅ TypeScript query syntax appears valid")
				return { success: true, syntaxOnly: true }
			} else {
				console.log("❌ TypeScript query syntax appears invalid")
				console.log("🔍 Query preview:", importQuery.substring(0, 200) + "...")
				return { success: false, error: "Invalid query syntax" }
			}
		}

		// Load TypeScript language parser
		console.log(`🌳 Loading TypeScript parser from: ${wasmPath}`)
		const language = await Parser.Language.load(wasmPath)
		console.log("✅ TypeScript language parser loaded")

		// Test query compilation
		console.log("🧪 Testing TypeScript query compilation...")
		const query = language.query(importQuery)
		console.log("✅ TypeScript query compiled successfully!")

		// Test query execution with sample code
		const testCode = `import type { User } from './types';
import { Component } from 'react';
import * as API from './api';
const fs = require('fs');`

		console.log("🧪 Testing query execution with sample TypeScript code...")
		const parser = new Parser()
		parser.setLanguage(language)
		const tree = parser.parse(testCode)
		const captures = query.captures(tree.rootNode)

		console.log(`🎯 Query executed successfully, found ${captures.length} captures`)

		// Show capture details
		if (captures.length > 0) {
			console.log("📋 Capture details:")
			captures.forEach((capture, i) => {
				console.log(`  ${i + 1}. ${capture.name}: "${capture.node.text.replace(/\n/g, "\\n")}"`)
			})
		}

		return { success: true, captureCount: captures.length, withParser: true }
	} catch (error) {
		console.log(`❌ TypeScript query binding test failed: ${error.message}`)

		// Detailed error analysis
		if (error.message.includes("Invalid node type")) {
			console.log("🔍 Analysis: Node type mismatch - check TypeScript grammar specification")
		} else if (error.message.includes("Invalid field")) {
			console.log("🔍 Analysis: Field name error - verify TypeScript node field names")
		} else if (error.message.includes("syntax error")) {
			console.log("🔍 Analysis: Query syntax error - check parentheses, quotes, and structure")
		} else if (error.message.includes("capture")) {
			console.log("🔍 Analysis: Capture group error - check @ symbols and capture names")
		}

		console.log(`📋 Error stack: ${error.stack}`)
		return { success: false, error: error.message, stack: error.stack }
	}
}

// Run both tests
async function runAllTests() {
	console.log("🚀 Starting comprehensive parser tests...\n")

	// Run the original real parser test
	const realParserResults = await testRealParsers()

	// Run the specific TypeScript test
	const typescriptResults = await testTypeScriptImportQuery()

	console.log("\n" + "=".repeat(60))
	console.log("🏆 FINAL TEST SUMMARY")
	console.log("=".repeat(60))

	console.log(`📊 Real Parser Tests: ${realParserResults.successCount} success, ${realParserResults.errorCount} errors`)
	console.log(`🎯 TypeScript Query Test: ${typescriptResults.success ? "SUCCESS" : "FAILED"}`)

	if (!typescriptResults.success) {
		console.log(`   TypeScript Error: ${typescriptResults.error}`)
	} else if (typescriptResults.captureCount !== undefined) {
		console.log(`   TypeScript Captures: ${typescriptResults.captureCount}`)
	}

	const totalErrors = realParserResults.errorCount + (typescriptResults.success ? 0 : 1)

	if (totalErrors > 0) {
		console.log(`\n🔧 Found ${totalErrors} total errors that need fixing`)
		process.exit(1)
	} else {
		console.log("\n🎉 All tests passed!")
		process.exit(0)
	}
}

// Run all tests
runAllTests().catch((error) => {
	console.error("💥 Unhandled error:", error)
	process.exit(1)
})
