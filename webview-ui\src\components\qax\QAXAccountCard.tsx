/**
 * <AUTHOR>
 */

import { useQaxAuth } from "@/hooks/useQaxAuth"
import { VSCodeButton, VSCodeProgressRing } from "@vscode/webview-ui-toolkit/react"
import { memo } from "react"

type QAXAccountCardProps = {
	className?: string
}

const QAXAccountCard = ({ className = "" }: QAXAccountCardProps) => {
	// 使用共享的 QAX 认证 Hook
	const { qaxUser, isAuthenticated, isLoading, handleLogin, handleLogout } = useQaxAuth()

	if (!isAuthenticated) {
		return (
			<div
				className={`p-5 border border-[var(--vscode-widget-border)] rounded-md bg-[var(--vscode-editor-background)] text-center ${className}`}>
				<h3 className="m-0 mb-4 text-base font-semibold text-[var(--vscode-foreground)]">QAX Codegen</h3>

				<VSCodeButton onClick={handleLogin} disabled={isLoading}>
					{isLoading ? (
						<div className="flex items-center gap-2">
							<VSCodeProgressRing style={{ width: "16px", height: "16px" }} />
							Connecting...
						</div>
					) : (
						"Connect to QAX Account"
					)}
				</VSCodeButton>
			</div>
		)
	}

	return (
		<div
			className={`p-5 border border-[var(--vscode-widget-border)] rounded-md bg-[var(--vscode-editor-background)] ${className}`}>
			<div className="flex justify-between items-center">
				<div>
					<h3 className="m-0 mb-1 text-base font-semibold text-[var(--vscode-foreground)]">QAX Account</h3>
					<p className="m-0 text-sm text-[var(--vscode-descriptionForeground)]">{qaxUser?.email || "No email"}</p>
				</div>

				<VSCodeButton appearance="secondary" onClick={handleLogout} disabled={isLoading}>
					{isLoading ? (
						<div className="flex items-center gap-2">
							<VSCodeProgressRing style={{ width: "16px", height: "16px" }} />
							Disconnecting...
						</div>
					) : (
						"Disconnect"
					)}
				</VSCodeButton>
			</div>
		</div>
	)
}

export default memo(QAXAccountCard)
