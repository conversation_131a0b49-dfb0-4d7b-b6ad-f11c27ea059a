/**
 * <AUTHOR>
 *
 * 共享的 QAX 认证 Hook
 * 用于统一管理 QAX 认证状态，避免在多个组件中重复实现相同的认证逻辑
 */

import { QaxServiceClient } from "@/services/grpc-client"
import { EmptyRequest } from "@shared/proto/common"
import { useCallback, useEffect, useState } from "react"

export type QAXUser = {
	email?: string
	displayName?: string
}

/**
 * QAX 认证 Hook
 * 提供统一的 QAX 认证状态管理和操作方法
 *
 * @returns {Object} 包含认证状态和操作方法的对象
 */
export const useQaxAuth = () => {
	const [qaxUser, setQaxUser] = useState<QAXUser | null>(null)
	const [isAuthenticated, setIsAuthenticated] = useState(false)
	const [isLoading, setIsLoading] = useState(false)

	// 检查认证状态 - 遵循 Cline 项目模式，使用空依赖数组避免无限循环
	const checkAuthState = useCallback(async () => {
		try {
			const response = await QaxServiceClient.qaxAuthStateChanged(EmptyRequest.create())
			if (response.user) {
				setQaxUser({
					email: response.user.email,
					displayName: response.user.displayName,
				})
				setIsAuthenticated(true)
			} else {
				setIsAuthenticated(false)
				setQaxUser(null)
			}
		} catch (error) {
			console.error("Failed to check QAX auth state:", error)
			setIsAuthenticated(false)
			setQaxUser(null)
		}
	}, []) // 遵循 Cline 项目模式：使用空依赖数组避免无限循环

	// 处理登录
	const handleLogin = useCallback(async () => {
		setIsLoading(true)
		try {
			const response = await QaxServiceClient.qaxLoginClicked(EmptyRequest.create())
			console.log("QAX login URL:", response.value)
			// 不需要手动检查认证状态，订阅事件会自动处理
		} catch (error) {
			console.error("QAX login failed:", error)
		} finally {
			setIsLoading(false)
		}
	}, [])

	// 处理登出
	const handleLogout = useCallback(async () => {
		setIsLoading(true)
		try {
			await QaxServiceClient.qaxLogoutClicked(EmptyRequest.create())
			// 登出后立即更新本地状态
			setIsAuthenticated(false)
			setQaxUser(null)
		} catch (error) {
			console.error("QAX logout failed:", error)
		} finally {
			setIsLoading(false)
		}
	}, [])

	// 组件挂载时初始化认证状态和订阅事件
	useEffect(() => {
		checkAuthState()

		// 订阅 QAX 认证回调事件
		const cleanup = QaxServiceClient.subscribeToQaxAuthCallback(EmptyRequest.create(), {
			onResponse: (event) => {
				console.log("QAX auth callback received:", event.value)
				// 重新检查认证状态
				checkAuthState()
			},
			onError: (error) => {
				console.error("Error in QAX authCallback subscription:", error)
			},
			onComplete: () => {},
		})

		return cleanup
	}, []) // 遵循 Cline 项目模式：只在组件挂载时执行一次

	return {
		// 状态
		qaxUser,
		isAuthenticated,
		isLoading,

		// 操作方法
		handleLogin,
		handleLogout,
		checkAuthState,
	}
}
