diff a/src/extension.ts b/src/extension.ts	(rejected hunks)
@@ -700,7 +700,18 @@ export async function activate(context: vscode.ExtensionContext) {
 	}
 	// 运行解析器功能测试
 	setTimeout(() => {
-		testParserFunctionality().catch(console.error)
+		//testParserFunctionality().catch(console.error)
+
+		// Test tree-sitter import parsing
+		import("./services/autocomplete/ContextGatherer").then(({ ContextGatherer }) => {
+			ContextGatherer.testImportParsing().catch(console.error)
+		})
+
+		// Run comprehensive ContextGatherer tests
+		//import("./services/autocomplete/test/SimpleContextTest").then(({ runSimpleContextTests }) => {
+		//	console.log("🧪 Running ContextGatherer functionality tests...")
+		//	runSimpleContextTests().catch(console.error)
+		//})
 	}, 3000) // 延迟3秒确保解析器完全加载
 
 	return createClineAPI(outputChannel, sidebarWebview.controller)
