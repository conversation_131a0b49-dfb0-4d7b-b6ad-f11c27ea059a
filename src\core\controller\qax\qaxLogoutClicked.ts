/**
 * <AUTHOR>
 */

import { QAXAccountService } from "../../../services/qax"
import type { EmptyRequest } from "../../../shared/proto/common"
import { Empty } from "../../../shared/proto/common"
import type { Controller } from "../index"

export async function qaxLogoutClicked(controller: Controller, _request: EmptyRequest): Promise<Empty> {
	const qaxService = new QAXAccountService(controller.context)
	await qaxService.logout()

	// 通知前端更新状态，确保所有组件都能感知到登出状态
	await controller.postStateToWebview()

	return Empty.create({})
}
