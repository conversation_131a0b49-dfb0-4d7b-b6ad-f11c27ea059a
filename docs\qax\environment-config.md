# QAX Environment Configuration

## 环境变量配置

| 环境变量 | 说明 | 默认值 |
|---------|------|--------|
| `QAX_ENVIRONMENT` | 环境类型：`development`, `testing`, `production` | `production` |
| `QAX_CODEGEN_DOMAIN` | 自定义QAX Codegen域名 | 根据环境自动选择 |

## 预设环境

- **development**: `https://codegen-dev.qianxin-inc.cn`
- **testing**: `https://codegen-test.qianxin-inc.cn`
- **production**: `https://codegen.qianxin-inc.cn` (默认)

## 配置方法

**Linux/macOS:**
```bash
export QAX_ENVIRONMENT=development
code
```

**Windows:**
```cmd
set QAX_ENVIRONMENT=development
code
```

**自定义域名:**
```bash
export QAX_CODEGEN_DOMAIN=https://your-domain.com
code
```

**注意**: 修改环境变量后需要重新启动VSCode
