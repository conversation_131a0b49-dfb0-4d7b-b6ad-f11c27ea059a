import { Controller } from ".."
import { QAXAccountService } from "../../../services/qax/QAXAccountService"
import { StringArray } from "../../../shared/proto/common"
import { OpenAiModelsRequest } from "../../../shared/proto/models"
import { QAXConfig } from "../../../utils/qax-config"

/**
 * Fetches available models from QAX Codegen
 * @param controller The controller instance
 * @param request The request (not used, uses JWT token from QAX Account)
 * @returns Array of QAX Codegen model names
 */
export async function getQaxCodegenModels(controller: Controller, request: OpenAiModelsRequest): Promise<StringArray> {
	try {
		// Get JWT token from QAX Account service
		const qaxService = new QAXAccountService(controller.context)
		const jwtToken = await qaxService.getJWTToken()

		// 使用统一的 JWT 验证工具
		if (!QAXConfig.isValidJWTToken(jwtToken || "")) {
			console.warn("QAX Codegen token is invalid or expired. Please login to QAX Account first.")
			// Return default models when not authenticated
			return StringArray.create({
				values: QAXConfig.DEFAULT_QAX_CODEGEN_MODELS,
			})
		}

		const baseUrl = QAXConfig.getCodegenApiBaseUrl()
		const response = await fetch(`${baseUrl}/chat/models`, {
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${jwtToken}`,
			},
		})

		if (!response.ok) {
			console.warn(`Failed to fetch QAX Codegen models (HTTP ${response.status})`)
			// Return default models on API failure
			return StringArray.create({
				values: QAXConfig.DEFAULT_QAX_CODEGEN_MODELS,
			})
		}

		const result = await response.json()
		const modelsData = result.data || []

		// Extract model IDs from the [displayName, modelId] format
		const modelIds = modelsData.map((model: [string, string]) => model[1])

		return StringArray.create({ values: modelIds })
	} catch (error) {
		console.warn("Failed to fetch QAX Codegen models:", error)
		// Return default models on any error
		return StringArray.create({
			values: QAXConfig.DEFAULT_QAX_CODEGEN_MODELS,
		})
	}
}
