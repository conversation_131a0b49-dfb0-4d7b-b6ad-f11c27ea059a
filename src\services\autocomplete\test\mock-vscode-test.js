/**
 * Test with mock vscode environment to replicate testImportParsing() conditions
 */

console.log("🔍 Starting Mock VSCode Import Parsing Test...")

// Mock vscode module
const mockVscode = {
	Position: class {
		constructor(line, character) {
			this.line = line
			this.character = character
		}
	},
	Range: class {
		constructor(start, end) {
			this.start = start
			this.end = end
		}
	},
	Uri: {
		file: (path) => ({ fsPath: path }),
	},
}

// Inject mock vscode into global scope
global.vscode = mockVscode

async function testWithMockVscode() {
	const path = require("path")

	// Now try to import the functions
	let getAst, getImportQuery
	try {
		// Clear require cache to ensure fresh import with mock vscode
		const modulePath = path.join(process.cwd(), "out/services/tree-sitter/languageParser")
		delete require.cache[require.resolve(modulePath)]

		const languageParser = require(modulePath)
		getAst = languageParser.getAst
		getImportQuery = languageParser.getImportQuery
		console.log("✅ Successfully imported languageParser with mock vscode")
	} catch (error) {
		console.error("❌ Failed to import languageParser even with mock vscode:", error.message)
		return { successCount: 0, errorCount: 1, errors: [{ language: "module-import", error: error.message }] }
	}

	// Test cases focusing on the problematic languages mentioned
	const testCases = [
		{
			name: "TypeScript",
			filepath: "test.ts",
			code: `import type { User } from './types';\nimport { Component } from 'react';`,
		},
		{
			name: "C++",
			filepath: "test.cpp",
			code: `#include <iostream>\n#include "header.h"\nusing namespace std;`,
		},
		{
			name: "C#",
			filepath: "test.cs",
			code: `using System;\nusing System.Collections.Generic;`,
		},
		{
			name: "PHP",
			filepath: "test.php",
			code: `<?php\nuse Namespace\\Class;\nrequire 'file.php';`,
		},
		{
			name: "Kotlin",
			filepath: "test.kt",
			code: `import package.Class\nimport package.Class as Alias`,
		},
		// Also test some that should work
		{
			name: "JavaScript",
			filepath: "test.js",
			code: `import { foo, bar } from 'module';\nconst fs = require('fs');`,
		},
		{
			name: "Python",
			filepath: "test.py",
			code: `import os\nfrom typing import List`,
		},
	]

	console.log("\n📋 Testing Problematic Languages with Mock VSCode...")
	console.log("=".repeat(70))

	let successCount = 0
	let errorCount = 0
	const errors = []

	for (const testCase of testCases) {
		try {
			console.log(`\n🧪 Testing ${testCase.name}...`)

			// Step 1: Test AST generation
			console.log(`  📝 Generating AST for ${testCase.name}...`)
			const ast = await getAst(testCase.filepath, testCase.code)

			if (!ast) {
				console.log(`  ❌ ${testCase.name}: AST generation failed`)
				errors.push({
					language: testCase.name,
					step: "AST Generation",
					error: "getAst() returned null",
					details: "Tree-sitter parser not available or failed to parse",
				})
				errorCount++
				continue
			}

			console.log(`  ✅ ${testCase.name}: AST generated`)

			// Step 2: Test import query loading
			console.log(`  📝 Loading import query for ${testCase.name}...`)
			const query = await getImportQuery(testCase.filepath)

			if (!query) {
				console.log(`  ❌ ${testCase.name}: Import query loading failed`)
				errors.push({
					language: testCase.name,
					step: "Import Query Loading",
					error: "getImportQuery() returned null",
					details: "Query compilation failed or language not supported",
				})
				errorCount++
				continue
			}

			console.log(`  ✅ ${testCase.name}: Import query loaded`)

			// Step 3: Test query execution
			console.log(`  📝 Executing query for ${testCase.name}...`)
			try {
				const matches = query.matches(ast.rootNode)
				console.log(`  ✅ ${testCase.name}: Query executed successfully (${matches.length} matches)`)

				// Analyze matches
				let importSymbols = 0
				for (const match of matches) {
					for (const capture of match.captures) {
						if (capture.name.startsWith("import.")) {
							importSymbols++
						}
					}
				}

				console.log(`  🎯 ${testCase.name}: Found ${importSymbols} import captures`)
				successCount++
			} catch (queryError) {
				console.log(`  ❌ ${testCase.name}: Query execution failed - ${queryError.message}`)
				errors.push({
					language: testCase.name,
					step: "Query Execution",
					error: queryError.message,
					details: "Query syntax error or incompatible node types",
				})
				errorCount++
			}
		} catch (error) {
			console.log(`  ❌ ${testCase.name}: General error - ${error.message}`)
			errors.push({
				language: testCase.name,
				step: "General",
				error: error.message,
				details: error.stack?.split("\n")[0] || "No stack trace",
			})
			errorCount++
		}
	}

	console.log("\n" + "=".repeat(70))
	console.log("📊 MOCK VSCODE TEST RESULTS")
	console.log("=".repeat(70))

	console.log(`✅ Successful: ${successCount}/${testCases.length}`)
	console.log(`❌ Failed: ${errorCount}/${testCases.length}`)

	if (errors.length > 0) {
		console.log("\n🚨 DETAILED FAILURE ANALYSIS:")
		console.log("-".repeat(70))

		// Group by step to identify patterns
		const stepGroups = {}
		errors.forEach((err) => {
			if (!stepGroups[err.step]) stepGroups[err.step] = []
			stepGroups[err.step].push(err.language)
		})

		console.log("\n📈 FAILURE PATTERNS:")
		Object.entries(stepGroups).forEach(([step, languages]) => {
			console.log(`${step}: ${languages.join(", ")}`)
		})

		console.log("\n🔍 INDIVIDUAL ERRORS:")
		errors.forEach((err, i) => {
			console.log(`${i + 1}. ${err.language} (${err.step}): ${err.error}`)
		})

		console.log("\n🔧 NEXT STEPS:")
		console.log("• Check if tree-sitter WASM files are missing for failing languages")
		console.log("• Verify import query syntax for languages failing at query loading")
		console.log("• Test query compilation with actual tree-sitter parsers")
	}

	console.log("\n" + "=".repeat(70))
	console.log("🏁 MOCK VSCODE TEST COMPLETED")
	console.log("=".repeat(70))

	return { successCount, errorCount, errors }
}

// Run the test
testWithMockVscode()
	.then((results) => {
		if (results.errorCount > 0) {
			console.log(`\n🔧 Identified ${results.errorCount} real issues matching testImportParsing() failures`)
			process.exit(1)
		} else {
			console.log("\n🎉 All tests passed with mock VSCode environment!")
			process.exit(0)
		}
	})
	.catch((error) => {
		console.error("💥 Unhandled error:", error)
		process.exit(1)
	})
