import * as path from "path"
const Parser = require("web-tree-sitter")
import {
	javascriptQuery,
	typescriptQuery,
	pythonQuery,
	rustQuery,
	goQuery,
	cppQuery,
	cQuery,
	csharpQuery,
	rubyQuery,
	javaQuery,
	phpQuery,
	swiftQuery,
	kotlinQuery,
} from "./queries"

// Import the new import queries
import { importQuery as javascriptImportQuery } from "./queries/javascript"
import { importQuery as typescriptImportQuery } from "./queries/typescript"
import { importQuery as pythonImportQuery } from "./queries/python"
import { importQuery as rustImportQuery } from "./queries/rust"
import { importQuery as goImportQuery } from "./queries/go"
import { importQuery as javaImportQuery } from "./queries/java"
import { importQuery as cppImportQuery } from "./queries/cpp"
import { importQuery as cImportQuery } from "./queries/c"
import { importQuery as csharpImportQuery } from "./queries/c-sharp"
import { importQuery as rubyImportQuery } from "./queries/ruby"
import { importQuery as phpImportQuery } from "./queries/php"
import { importQuery as swiftImportQuery } from "./queries/swift"
import { importQuery as kotlinImportQuery } from "./queries/kotlin"

export interface LanguageParser {
	[key: string]: {
		parser: any
		query: any
	}
}

export interface CachedParser {
	parser: any
	query: any
	language: any
}

// Global cache for parsers
const parserCache = new Map<string, CachedParser>()
let isParserInitialized = false

async function loadLanguage(langName: string) {
	// Try multiple possible paths for WASM files
	const possiblePaths = [
		path.join(__dirname, `tree-sitter-${langName}.wasm`), // Current directory (for compiled output)
		path.join(__dirname, "..", "..", "..", "dist", `tree-sitter-${langName}.wasm`), // dist directory from out/services/tree-sitter
		path.join(process.cwd(), "dist", `tree-sitter-${langName}.wasm`), // dist directory from workspace root
		path.join(__dirname, "..", "..", "..", "node_modules", "tree-sitter-wasms", "out", `tree-sitter-${langName}.wasm`), // node_modules fallback
	]

	for (const wasmPath of possiblePaths) {
		try {
			const fs = require("fs")
			if (fs.existsSync(wasmPath)) {
				return await Parser.Language.load(wasmPath)
			}
		} catch (error) {
			// Continue to next path
		}
	}

	// If all paths fail, throw an error with helpful information
	throw new Error(`Could not find WASM file for language: ${langName}. Tried paths: ${possiblePaths.join(", ")}`)
}

async function initializeParser() {
	if (!isParserInitialized) {
		await Parser.init()
		isParserInitialized = true
	}
}

// Language extension mapping
const LANGUAGE_EXTENSIONS: Record<string, string> = {
	js: "javascript",
	jsx: "javascript",
	ts: "typescript",
	tsx: "typescript", // tsx uses typescript parser
	py: "python",
	rs: "rust",
	go: "go",
	cpp: "cpp",
	cc: "cpp",
	cxx: "cpp",
	hpp: "cpp",
	c: "c",
	h: "c",
	cs: "c_sharp",
	rb: "ruby",
	java: "java",
	php: "php",
	swift: "swift",
	kt: "kotlin",
}

/*
Using node bindings for tree-sitter is problematic in vscode extensions 
because of incompatibility with electron. Going the .wasm route has the 
advantage of not having to build for multiple architectures.

We use web-tree-sitter and tree-sitter-wasms which provides auto-updating prebuilt WASM binaries for tree-sitter's language parsers.

This function loads WASM modules for relevant language parsers based on input files:
1. Extracts unique file extensions
2. Maps extensions to language names
3. Loads corresponding WASM files (containing grammar rules)
4. Uses WASM modules to initialize tree-sitter parsers

This approach optimizes performance by loading only necessary parsers once for all relevant files.

Sources:
- https://github.com/tree-sitter/node-tree-sitter/issues/169
- https://github.com/tree-sitter/node-tree-sitter/issues/168
- https://github.com/Gregoor/tree-sitter-wasms/blob/main/README.md
- https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/README.md
- https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/test/query-test.js
*/
export async function loadRequiredLanguageParsers(filesToParse: string[]): Promise<LanguageParser> {
	await initializeParser()
	const extensionsToLoad = new Set(filesToParse.map((file) => path.extname(file).toLowerCase().slice(1)))
	const parsers: LanguageParser = {}
	for (const ext of Array.from(extensionsToLoad)) {
		let language: any
		let query: any
		switch (ext) {
			case "js":
			case "jsx":
				language = await loadLanguage("javascript")
				query = language.query(javascriptQuery)
				break
			case "ts":
				language = await loadLanguage("typescript")
				query = language.query(typescriptQuery)
				break
			case "tsx":
				language = await loadLanguage("typescript")
				query = language.query(typescriptQuery)
				break
			case "py":
				language = await loadLanguage("python")
				query = language.query(pythonQuery)
				break
			case "rs":
				language = await loadLanguage("rust")
				query = language.query(rustQuery)
				break
			case "go":
				language = await loadLanguage("go")
				query = language.query(goQuery)
				break
			case "cpp":
			case "cc":
			case "cxx":
			case "hpp":
				language = await loadLanguage("cpp")
				query = language.query(cppQuery)
				break
			case "c":
			case "h":
				language = await loadLanguage("c")
				query = language.query(cQuery)
				break
			case "cs":
				language = await loadLanguage("c_sharp")
				query = language.query(csharpQuery)
				break
			case "rb":
				language = await loadLanguage("ruby")
				query = language.query(rubyQuery)
				break
			case "java":
				language = await loadLanguage("java")
				query = language.query(javaQuery)
				break
			case "php":
				language = await loadLanguage("php")
				query = language.query(phpQuery)
				break
			case "swift":
				language = await loadLanguage("swift")
				query = language.query(swiftQuery)
				break
			case "kt":
				language = await loadLanguage("kotlin")
				query = language.query(kotlinQuery)
				break
			default:
				throw new Error(`Unsupported language: ${ext}`)
		}
		const parser = new Parser()
		parser.setLanguage(language)
		parsers[ext] = { parser, query }
	}
	return parsers
}

/**
 * Pre-loads all supported tree-sitter parsers during extension startup.
 * This improves performance by avoiding parser loading during completion requests.
 */
export async function preloadAllParsers(): Promise<void> {
	await initializeParser()

	console.log("🌳 Pre-loading tree-sitter parsers...")

	const supportedExtensions = Object.keys(LANGUAGE_EXTENSIONS)
	const loadPromises = supportedExtensions.map(async (ext) => {
		try {
			const langName = LANGUAGE_EXTENSIONS[ext]

			// Check if already cached
			if (parserCache.has(ext)) {
				return
			}

			const language = await loadLanguage(langName)
			const query = getQueryForLanguage(langName, language)
			const parser = new Parser()
			parser.setLanguage(language)

			parserCache.set(ext, { parser, query, language })
			console.log(`🌳 Loaded parser for .${ext} (${langName})`)
		} catch (error) {
			console.warn(`🌳 Failed to load parser for .${ext}:`, error)
		}
	})

	await Promise.all(loadPromises)
	console.log(`🌳 Pre-loaded ${parserCache.size} tree-sitter parsers`)
}

/**
 * Gets a cached parser for the given file extension.
 * Returns undefined if no parser is available for the extension.
 */
export function getCachedParser(fileExtension: string): CachedParser | undefined {
	const ext = fileExtension.toLowerCase().replace(".", "")
	return parserCache.get(ext)
}

/**
 * Gets a parser for a specific file path.
 * Uses cached parsers when available, falls back to on-demand loading.
 */
export async function getParserForFile(filepath: string): Promise<any | undefined> {
	const ext = path.extname(filepath).toLowerCase().slice(1)

	// Try to get from cache first
	const cached = getCachedParser(ext)
	if (cached) {
		// Create a new parser instance to avoid conflicts
		const parser = new Parser()
		parser.setLanguage(cached.language)
		return parser
	}

	// Fall back to on-demand loading for unsupported extensions
	return undefined
}

function getQueryForLanguage(langName: string, language: any): any {
	switch (langName) {
		case "javascript":
			return language.query(javascriptQuery)
		case "typescript":
			return language.query(typescriptQuery)
		case "python":
			return language.query(pythonQuery)
		case "rust":
			return language.query(rustQuery)
		case "go":
			return language.query(goQuery)
		case "cpp":
			return language.query(cppQuery)
		case "c":
			return language.query(cQuery)
		case "c_sharp":
			return language.query(csharpQuery)
		case "ruby":
			return language.query(rubyQuery)
		case "java":
			return language.query(javaQuery)
		case "php":
			return language.query(phpQuery)
		case "swift":
			return language.query(swiftQuery)
		case "kotlin":
			return language.query(kotlinQuery)
		default:
			throw new Error(`Unsupported language: ${langName}`)
	}
}

/**
 * Gets the import query for a specific language.
 * This is separate from definition queries to allow focused import parsing.
 */
function getImportQueryForLanguage(langName: string, language: any): any | null {
	try {
		switch (langName) {
			case "javascript":
				return language.query(javascriptImportQuery)
			case "typescript":
				return language.query(typescriptImportQuery)
			case "python":
				return language.query(pythonImportQuery)
			case "rust":
				return language.query(rustImportQuery)
			case "go":
				return language.query(goImportQuery)
			case "java":
				return language.query(javaImportQuery)
			case "cpp":
				return language.query(cppImportQuery)
			case "c":
				return language.query(cImportQuery)
			case "c_sharp":
				return language.query(csharpImportQuery)
			case "ruby":
				return language.query(rubyImportQuery)
			case "php":
				return language.query(phpImportQuery)
			case "swift":
				return language.query(swiftImportQuery)
			case "kotlin":
				return language.query(kotlinImportQuery)
			default:
				return null // Return null for unsupported languages instead of throwing
		}
	} catch (error) {
		console.warn(`Failed to create import query for ${langName}:`, error)
		return null
	}
}

// Consolidated AST utility functions
export type AstPath = any[]

/**
 * Creates an AST from file content using the appropriate parser.
 * Consolidated version of getAst functions from autocomplete utils.
 */
export async function getAst(filepath: string, fileContents: string): Promise<any | undefined> {
	const parser = await getParserForFile(filepath)
	if (!parser) {
		return undefined
	}

	try {
		const ast = parser.parse(fileContents)
		return ast || undefined
	} catch (e) {
		return undefined
	}
}

/**
 * Gets the path from root to the node containing the cursor position.
 * Consolidated version of getTreePathAtCursor functions from autocomplete utils.
 */
export async function getTreePathAtCursor(ast: any, cursorIndex: number): Promise<AstPath> {
	const path = [ast.rootNode]
	while (path[path.length - 1].childCount > 0) {
		let foundChild = false
		for (const child of path[path.length - 1].children) {
			if (child && child.startIndex <= cursorIndex && child.endIndex >= cursorIndex) {
				path.push(child)
				foundChild = true
				break
			}
		}

		if (!foundChild) {
			break
		}
	}

	return path
}

/**
 * Gets an import query for a specific file.
 * This provides a consistent interface for import parsing across languages.
 */
export async function getImportQuery(filepath: string): Promise<any | null> {
	const ext = path.extname(filepath).toLowerCase().slice(1)
	const cached = getCachedParser(ext)

	if (!cached) {
		return null
	}

	const langName = getLanguageNameFromFilepath(filepath)
	if (!langName) {
		return null
	}

	return getImportQueryForLanguage(langName, cached.language)
}

/**
 * Helper function to determine language name from file path.
 */
function getLanguageNameFromFilepath(filepath: string): string | null {
	const extension = filepath.split(".").pop()?.toLowerCase()

	switch (extension) {
		case "js":
		case "jsx":
		case "mjs":
			return "javascript"
		case "ts":
		case "tsx":
			return "typescript"
		case "py":
		case "pyi":
			return "python"
		case "rs":
			return "rust"
		case "go":
			return "go"
		case "java":
			return "java"
		case "cpp":
		case "cc":
		case "cxx":
		case "c++":
			return "cpp"
		case "c":
		case "h":
			return "c"
		case "cs":
			return "c_sharp"
		case "rb":
			return "ruby"
		case "php":
			return "php"
		case "swift":
			return "swift"
		case "kt":
		case "kts":
			return "kotlin"
		default:
			return null
	}
}
