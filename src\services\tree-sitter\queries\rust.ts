/*
- struct definitions
- method definitions
- function definitions
*/
export const definitionQuery = `
(struct_item
    name: (type_identifier) @name.definition.class) @definition.class

(declaration_list
    (function_item
        name: (identifier) @name.definition.method)) @definition.method

(function_item
    name: (identifier) @name.definition.function) @definition.function
`

/*
- Rust use statements and extern crate declarations
*/
export const importQuery = `
; use module::symbol;
(use_declaration
  argument: (identifier) @import.name) @import.statement

; use module::{symbol1, symbol2};
(use_declaration
  argument: (scoped_use_list
    path: (identifier) @import.source
    list: (use_list
      (identifier) @import.name))) @import.statement

; use module::symbol as alias;
(use_declaration
  argument: (use_as_clause
    path: (identifier) @import.name
    alias: (identifier) @import.alias)) @import.statement

; extern crate name;
(extern_crate_declaration
  name: (identifier) @import.name) @import.statement
`

// Default export for backward compatibility
export default definitionQuery
