import { Controller } from "../index"
import { StringRequest } from "../../../shared/proto/common"

/**
 * Enhances a given prompt using an API provider
 * @param controller The controller instance
 * @param request The request containing the prompt to enhance
 * @returns Object with the enhanced prompt
 */
export async function enhancePrompt(controller: Controller, request: StringRequest): Promise<{ value: string }> {
	const enhancedPrompt = await controller.enhancePrompt(request.value || "")
	return {
		value: enhancedPrompt,
	}
}
