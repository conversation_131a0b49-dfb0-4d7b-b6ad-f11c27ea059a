diff a/src/integrations/git/commit-message-generator.ts b/src/integrations/git/commit-message-generator.ts	(rejected hunks)
@@ -22,11 +22,17 @@ function formatGitDiffPrompt(gitDiff: string): string {
 
 ${truncatedDiff}
 
-The commit message should:
-1. Start with a short summary (50-72 characters)
-2. Use the imperative mood (e.g., "Add feature" not "Added feature")
-3. Describe what was changed and why
-4. Be clear and descriptive
+Generate a professional commit message in this format:
+:  (#issue)
+
+<detail 1>
+<detail 2>
+
+Rules:  
+1. Use conventional commit types (feat|fix|docs|style|refactor|test|chore).  
+2. Keep title under 50 chars. Reference issues like (#123).  
+3. List key changes as bullet points.  
+4. Be clear and technical but omit low-level details.  
 
 Commit message:`
 }
