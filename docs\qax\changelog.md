# QAX 功能变更日志

## 2025-07-08 (更新)

### UI 优化和本地化改进
- 优化 WelcomeView 欢迎页面，支持 QAX 认证状态实时更新
- 将 TelemetryBanner 遥测横幅改为 QAXCodegen 功能介绍
- 完善中文本地化，提升用户体验

### 修改文件
- `webview-ui/src/components/welcome/WelcomeView.tsx` - 集成 QAX 认证状态管理，支持登录状态实时刷新
- `webview-ui/src/components/common/TelemetryBanner.tsx` - 替换遥测说明为 QAXCodegen 功能介绍

## 2025-07-08 (初始版本)

### 新增功能
- 实现 QAX 和 QAX Codegen 两个新的 API 提供商
- 添加 QAX 账户认证系统，支持 JWT token 认证
- 实现 QAX Codegen 模型选择和配置功能
- 添加统一的 QAX 配置管理工具类

### 新增文件

#### 后端核心文件
- `src/api/providers/qax-codegen.ts` - QAX Codegen API 处理器，支持 JWT 认证和多种模型格式
- `src/core/controller/models/getQaxModels.ts` - QAX 模型列表获取服务
- `src/core/controller/models/getQaxCodegenModels.ts` - QAX Codegen 模型列表获取服务
- `src/utils/qax-config.ts` - QAX 统一配置工具类，管理域名、URL 和 JWT token 验证

#### 前端组件文件
- `webview-ui/src/components/settings/providers/QaxCodegenProvider.tsx` - QAX Codegen 设置页面组件
- `webview-ui/src/hooks/useQaxAuth.ts` - QAX 认证状态管理 Hook

### 修改文件

#### 协议和类型定义
- `proto/models.proto` - 添加 QAX 和 QAX_CODEGEN 枚举值，新增相关 RPC 方法和配置字段
- `src/shared/api.ts` - 扩展 ApiProvider 类型和 ApiHandlerOptions 接口，添加 QAX 相关配置选项
- `src/shared/WebviewMessage.ts` - 更新消息类型定义,移除 fetchModels

#### API 和服务层
- `src/api/index.ts` - 注册 QAX 和 QAX Codegen 提供商，更新导入路径（qax_codegen.ts → qax-codegen.ts）
- `src/api/providers/qax.ts` - QAX API 处理器实现，支持多种模型格式和推理模式
- `src/services/qax/QAXAccountService.ts` - QAX 账户服务，管理 JWT token 存储和用户信息

#### 控制器和状态管理
- `src/core/controller/index.ts` - 集成 QAX 相关的 gRPC 服务处理
- `src/core/controller/qax/qaxLogoutClicked.ts` - QAX 登出处理逻辑
- `src/core/storage/state-keys.ts` - 添加 QAX 相关状态键定义
- `src/core/storage/state.ts` - 扩展状态管理以支持 QAX 配置
- `src/shared/proto-conversions/models/api-configuration-conversion.ts` - 添加 QAX 配置转换逻辑

#### 前端界面组件
- `webview-ui/src/components/qax/QAXAccountCard.tsx` - QAX 账户信息卡片组件
- `webview-ui/src/components/settings/ApiOptions.tsx` - 集成 QAX 提供商选项
- `webview-ui/src/components/settings/providers/QaxProvider.tsx` - QAX 设置页面组件
- `webview-ui/src/components/settings/utils/providerUtils.ts` - 添加 QAX 提供商工具函数
- `webview-ui/src/components/chat/ChatTextArea.tsx` - 支持 QAX 提供商的聊天界面
