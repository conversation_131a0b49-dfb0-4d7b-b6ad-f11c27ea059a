/**
 * <AUTHOR>
 */

import * as vscode from "vscode"
import type { EmptyRequest } from "../../../shared/proto/common"
import { String } from "../../../shared/proto/common"
import type { Controller } from "../index"

export async function qaxLoginClicked(controller: Controller, _request: EmptyRequest): Promise<String> {
	// Open browser for authentication with ref param
	console.log("QAX Login button clicked")
	console.log("Opening QAX auth page with ref param")

	const uriScheme = vscode.env.uriScheme
	const callbackUrl = `${uriScheme || "vscode"}://saoudrizwan.claude-dev/qax-auth`

	// 使用 QAXAccountService 构建认证 URL
	const { QAXAccountService } = await import("../../../services/qax")
	const qaxService = new QAXAccountService(controller.context)
	const authUrlString = qaxService.getAuthUrl(encodeURIComponent(callbackUrl))

	const authUrl = vscode.Uri.parse(authUrlString)
	await vscode.env.openExternal(authUrl)

	return String.create({ value: authUrl.toString() })
}
