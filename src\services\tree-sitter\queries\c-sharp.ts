/*
- class declarations
- interface declarations
- method declarations
- namespace declarations
*/
export const definitionQuery = `
(class_declaration
 name: (identifier) @name.definition.class
) @definition.class

(interface_declaration
 name: (identifier) @name.definition.interface
) @definition.interface

(method_declaration
 name: (identifier) @name.definition.method
) @definition.method

(namespace_declaration
 name: (identifier) @name.definition.module
) @definition.module
`

/*
- C# using statements and namespace imports
*/
export const importQuery = `
; using System;
(using_directive
  (identifier) @import.name) @import.statement

; using System.Collections;
(using_directive
  (qualified_name) @import.name) @import.statement

; using alias = System.Collections.Generic;
(using_directive
  (identifier) @import.alias
  (qualified_name) @import.name) @import.statement
`

// Default export for backward compatibility
export default definitionQuery
