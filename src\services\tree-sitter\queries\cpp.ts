/*
- struct declarations
- union declarations
- function declarations
- method declarations (with namespace scope)
- typedef declarations
- class declarations
*/
export const definitionQuery = `
(struct_specifier (type_identifier) @definition.class body:(_)) @definition.class

(declaration type: (union_specifier (type_identifier) @definition.class)) @definition.class

(function_declarator declarator: (identifier) @name.definition.function) @definition.function

(function_declarator declarator: (field_identifier) @name.definition.function) @definition.function

(function_declarator declarator: (qualified_identifier scope: (namespace_identifier) @scope (identifier) @definition.method)) @definition.method

(type_definition declarator: (type_identifier) @name.definition.type) @definition.type

(class_specifier (type_identifier) @definition.class) @definition.class
`

/*
- C++ #include statements and using declarations
*/
export const importQuery = `
; #include <header>
(preproc_include
  path: (system_lib_string) @import.source) @import.statement

; #include "header"
(preproc_include
  path: (string_literal) @import.source) @import.statement

; using namespace std;
(using_declaration
  (identifier) @import.name) @import.statement

; using std::cout;
(using_declaration
  (qualified_identifier
    (identifier) @import.name)) @import.statement
`

// Default export for backward compatibility
export default definitionQuery
