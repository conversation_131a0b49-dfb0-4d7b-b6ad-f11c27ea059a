/**
 * <AUTHOR>
 */

import { memo } from "react"
import QAXAccountCard from "./QAXAccountCard"

type QAXAccountViewProps = {
	className?: string
}

const QAXAccountView = ({ className = "" }: QAXAccountViewProps) => {
	return (
		<div className={`p-4 max-w-3xl mx-auto ${className}`}>
			<div className="text-center mb-8">
				<h2 className="m-0 mb-4 text-2xl font-semibold text-[var(--vscode-foreground)]">QAX Codegen</h2>
				<p className="m-0 text-[var(--vscode-descriptionForeground)]">登录 QAX 账户以使用 QAX Codegen 服务</p>
			</div>

			<QAXAccountCard />
		</div>
	)
}

export default memo(QAXAccountView)
