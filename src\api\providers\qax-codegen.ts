import { Anthropic } from "@anthropic-ai/sdk"
import { ApiHandlerOptions, ModelInfo, openAiModelInfoSaneDefaults } from "@shared/api"
import OpenAI from "openai"
import type { ChatCompletionReasoningEffort } from "openai/resources/chat/completions"
import { QAXConfig } from "../../utils/qax-config"
import { ApiHandler } from "../index"
import { withRetry } from "../retry"
import { convertToOpenAiMessages } from "../transform/openai-format"
import { convertToR1Format } from "../transform/r1-format"
import { ApiStream } from "../transform/stream"

export class QaxCodegenHandler implements ApiHandler {
	private options: ApiHandlerOptions
	private client: OpenAI

	constructor(options: ApiHandlerOptions) {
		this.options = options

		// 创建 OpenAI 客户端，使用 JWT token 作为 apiKey
		this.client = new OpenAI({
			baseURL: QAXConfig.getCodegenApiBaseUrl(),
			apiKey: this.options.qaxCodegenToken || "placeholder",
		})
	}

	/**
	 * 获取当前有效的 JWT token
	 */
	private async getValidJwtToken(): Promise<string> {
		const token = this.options.qaxCodegenToken

		// 使用统一的 JWT 验证工具
		QAXConfig.validateJWTToken(token || "")

		return token!
	}

	@withRetry()
	async *createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream {
		// 始终动态获取 JWT token 并更新客户端的 apiKey
		const jwtToken = await this.getValidJwtToken()

		// 动态更新客户端的 apiKey
		this.client.apiKey = jwtToken

		const modelId = this.getModel().id
		// 检测 DeepSeek R1 模型：包含 "deepseek-reasoner" 或者 "deepseek" + "r1" 的组合
		const isDeepseekReasoner = /deepseek-reasoner|deepseek.*r1/.test(modelId.toLowerCase())
		const isR1FormatRequired = this.options.qaxCodegenModelInfo?.isR1FormatRequired ?? isDeepseekReasoner
		const isReasoningModelFamily = modelId.includes("o1") || modelId.includes("o3") || modelId.includes("o4")

		let openAiMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[]
		let temperature: number | undefined = 0.7
		let maxTokens = this.getModel().info.maxTokens || 4096
		let reasoningEffort: ChatCompletionReasoningEffort | undefined = undefined

		if (isR1FormatRequired) {
			// 使用 R1 格式转换
			openAiMessages = convertToR1Format([{ role: "user", content: systemPrompt }, ...messages])
		} else {
			// 使用标准 OpenAI 格式转换
			openAiMessages = [{ role: "system", content: systemPrompt }, ...convertToOpenAiMessages(messages)]
		}

		if (isReasoningModelFamily) {
			openAiMessages = [{ role: "developer", content: systemPrompt }, ...convertToOpenAiMessages(messages)]
			temperature = undefined // does not support temperature
			reasoningEffort = (this.options.reasoningEffort as ChatCompletionReasoningEffort) || "medium"
		}

		const stream = await this.client.chat.completions.create({
			model: modelId,
			messages: openAiMessages,
			temperature,
			max_tokens: maxTokens,
			reasoning_effort: reasoningEffort,
			stream: true,
			stream_options: { include_usage: true },
		})
		for await (const chunk of stream) {
			const delta = chunk.choices[0]?.delta
			if (delta?.content) {
				yield {
					type: "text",
					text: delta.content,
				}
			}

			if (delta && "reasoning_content" in delta && delta.reasoning_content) {
				yield {
					type: "reasoning",
					reasoning: (delta.reasoning_content as string | undefined) || "",
				}
			}

			if (chunk.usage) {
				yield {
					type: "usage",
					inputTokens: chunk.usage.prompt_tokens || 0,
					outputTokens: chunk.usage.completion_tokens || 0,
					// @ts-ignore-next-line
					cacheReadTokens: chunk.usage.prompt_tokens_details?.cached_tokens || 0,
					// @ts-ignore-next-line
					cacheWriteTokens: chunk.usage.prompt_cache_miss_tokens || 0,
				}
			}
		}
	}

	getModel(): { id: string; info: ModelInfo } {
		return {
			id: this.options.qaxCodegenModelId ?? QAXConfig.DEFAULT_QAX_CODEGEN_MODEL,
			info: this.options.qaxCodegenModelInfo ?? openAiModelInfoSaneDefaults,
		}
	}
}
