/**
 * Comprehensive test that replicates the exact conditions used by testImportParsing() in ContextGatherer.ts
 * This test uses the real tree-sitter parser initialization and language loading process
 */

console.log("🔍 Starting Real Import Parsing Test...")

async function testRealImportParsing() {
	const path = require("path")

	// Import the actual functions used by testImportParsing()
	let getAst, getImportQuery
	try {
		const modulePath = path.join(process.cwd(), "out/services/tree-sitter/languageParser")
		const languageParser = require(modulePath)
		getAst = languageParser.getAst
		getImportQuery = languageParser.getImportQuery
		console.log("✅ Successfully imported real languageParser functions")
	} catch (error) {
		console.error("❌ Failed to import languageParser:", error.message)
		return { successCount: 0, errorCount: 1, errors: [{ language: "module-import", error: error.message }] }
	}

	// Test cases that mirror the ones used in testImportParsing()
	const testCases = [
		{
			name: "JavaScript",
			filepath: "test.js",
			code: `import { foo, bar } from 'module';\nconst fs = require('fs');`,
		},
		{
			name: "TypeScript",
			filepath: "test.ts",
			code: `import type { User } from './types';\nimport { Component } from 'react';`,
		},
		{
			name: "Python",
			filepath: "test.py",
			code: `import os\nfrom typing import List\nimport numpy as np`,
		},
		{
			name: "Rust",
			filepath: "test.rs",
			code: `use std::collections::HashMap;\nuse serde::{Deserialize, Serialize};`,
		},
		{
			name: "Go",
			filepath: "test.go",
			code: `import "fmt"\nimport json "encoding/json"`,
		},
		{
			name: "Java",
			filepath: "test.java",
			code: `import java.util.List;\nimport static java.lang.Math.PI;`,
		},
		{
			name: "C++",
			filepath: "test.cpp",
			code: `#include <iostream>\n#include "header.h"\nusing namespace std;`,
		},
		{
			name: "C",
			filepath: "test.c",
			code: `#include <stdio.h>\n#include "local.h"`,
		},
		{
			name: "C#",
			filepath: "test.cs",
			code: `using System;\nusing System.Collections.Generic;`,
		},
		{
			name: "Ruby",
			filepath: "test.rb",
			code: `require 'json'\ninclude Enumerable`,
		},
		{
			name: "PHP",
			filepath: "test.php",
			code: `<?php\nuse Namespace\\Class;\nrequire 'file.php';`,
		},
		{
			name: "Swift",
			filepath: "test.swift",
			code: `import Foundation\nimport class UIKit.UIView`,
		},
		{
			name: "Kotlin",
			filepath: "test.kt",
			code: `import package.Class\nimport package.Class as Alias`,
		},
	]

	console.log("\n📋 Testing Real Import Parsing (Exact testImportParsing() Conditions)...")
	console.log("=".repeat(80))

	let successCount = 0
	let errorCount = 0
	const errors = []

	for (const testCase of testCases) {
		try {
			console.log(`\n🧪 Testing ${testCase.name} import parsing...`)

			// Step 1: Test AST generation (same as testImportParsing())
			console.log(`  📝 ${testCase.name}: Generating AST...`)
			const ast = await getAst(testCase.filepath, testCase.code)

			if (!ast) {
				console.log(`  ❌ ${testCase.name}: Failed to generate AST`)
				errors.push({
					language: testCase.name,
					step: "AST Generation",
					error: "getAst() returned null",
					details: "Parser may not be available for this language",
				})
				errorCount++
				continue
			}

			console.log(`  ✅ ${testCase.name}: AST generated successfully`)

			// Step 2: Test import query loading (same as testImportParsing())
			console.log(`  📝 ${testCase.name}: Loading import query...`)
			const query = await getImportQuery(testCase.filepath)

			if (!query) {
				console.log(`  ❌ ${testCase.name}: Failed to load import query`)
				errors.push({
					language: testCase.name,
					step: "Import Query Loading",
					error: "getImportQuery() returned null",
					details: "Query compilation may have failed",
				})
				errorCount++
				continue
			}

			console.log(`  ✅ ${testCase.name}: Import query loaded successfully`)

			// Step 3: Test query execution (same as extractImportedSymbolsWithTreeSitter())
			console.log(`  📝 ${testCase.name}: Executing query on AST...`)
			try {
				const matches = query.matches(ast.rootNode)
				console.log(`  ✅ ${testCase.name}: Query executed, found ${matches.length} matches`)

				// Process matches like the real implementation
				let symbolCount = 0
				for (const match of matches) {
					for (const capture of match.captures) {
						if (capture.name === "import.name" || capture.name === "import.source") {
							symbolCount++
						}
					}
				}

				console.log(`  🎯 ${testCase.name}: Extracted ${symbolCount} import symbols`)
				successCount++
			} catch (queryError) {
				console.log(`  ❌ ${testCase.name}: Query execution failed - ${queryError.message}`)
				errors.push({
					language: testCase.name,
					step: "Query Execution",
					error: queryError.message,
					details: "Query may have syntax errors or incompatible node types",
				})
				errorCount++
			}
		} catch (error) {
			console.log(`  ❌ ${testCase.name}: Test error - ${error.message}`)
			errors.push({
				language: testCase.name,
				step: "General Test",
				error: error.message,
				details: error.stack,
			})
			errorCount++
		}
	}

	console.log("\n" + "=".repeat(80))
	console.log("📊 REAL IMPORT PARSING TEST RESULTS")
	console.log("=".repeat(80))

	console.log(`✅ Successful: ${successCount}/${testCases.length}`)
	console.log(`❌ Failed: ${errorCount}/${testCases.length}`)

	if (errors.length > 0) {
		console.log("\n🚨 DETAILED ERROR ANALYSIS:")
		console.log("-".repeat(80))

		errors.forEach((err, index) => {
			console.log(`\n${index + 1}. ${err.language} (${err.step}):`)
			console.log(`   Error: ${err.error}`)
			console.log(`   Details: ${err.details}`)
		})

		// Group errors by step
		const errorsByStep = {}
		errors.forEach((err) => {
			if (!errorsByStep[err.step]) {
				errorsByStep[err.step] = []
			}
			errorsByStep[err.step].push(err.language)
		})

		console.log("\n📈 ERROR PATTERNS BY STEP:")
		console.log("-".repeat(50))
		Object.entries(errorsByStep).forEach(([step, languages]) => {
			console.log(`${step}: ${languages.join(", ")}`)
		})

		console.log("\n🔧 DEBUGGING RECOMMENDATIONS:")
		console.log("-".repeat(50))
		console.log("• AST Generation failures: Check if tree-sitter WASM files are available")
		console.log("• Import Query Loading failures: Verify query syntax and node types")
		console.log("• Query Execution failures: Check for incompatible capture names or node structures")
		console.log("• Run with VS Code extension to see actual error messages in debug console")
	}

	console.log("\n" + "=".repeat(80))
	console.log("🏁 REAL IMPORT PARSING TEST COMPLETED")
	console.log("=".repeat(80))

	return { successCount, errorCount, errors }
}

// Run the test
testRealImportParsing()
	.then((results) => {
		if (results.errorCount > 0) {
			console.log(`\n🔧 Found ${results.errorCount} real import parsing errors`)
			console.log("These match the errors seen in testImportParsing()!")
			process.exit(1)
		} else {
			console.log("\n🎉 All real import parsing tests passed!")
			process.exit(0)
		}
	})
	.catch((error) => {
		console.error("💥 Unhandled error:", error)
		process.exit(1)
	})
