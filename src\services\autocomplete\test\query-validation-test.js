/**
 * Validate individual import queries for syntax errors
 */

console.log("🔍 Starting Import Query Validation Test...")

async function validateImportQueries() {
	const fs = require("fs")

	const queryFiles = [
		{ name: "JavaScript", file: "src/services/tree-sitter/queries/javascript.ts" },
		{ name: "TypeScript", file: "src/services/tree-sitter/queries/typescript.ts" },
		{ name: "Python", file: "src/services/tree-sitter/queries/python.ts" },
		{ name: "Rust", file: "src/services/tree-sitter/queries/rust.ts" },
		{ name: "Go", file: "src/services/tree-sitter/queries/go.ts" },
		{ name: "Java", file: "src/services/tree-sitter/queries/java.ts" },
		{ name: "C++", file: "src/services/tree-sitter/queries/cpp.ts" },
		{ name: "C", file: "src/services/tree-sitter/queries/c.ts" },
		{ name: "C#", file: "src/services/tree-sitter/queries/c-sharp.ts" },
		{ name: "Ruby", file: "src/services/tree-sitter/queries/ruby.ts" },
		{ name: "PHP", file: "src/services/tree-sitter/queries/php.ts" },
		{ name: "Swift", file: "src/services/tree-sitter/queries/swift.ts" },
		{ name: "Kotlin", file: "src/services/tree-sitter/queries/kotlin.ts" },
	]

	console.log("\n📋 Validating Import Query Syntax...")
	console.log("=".repeat(60))

	let successCount = 0
	let errorCount = 0
	const errors = []

	for (const { name, file } of queryFiles) {
		try {
			console.log(`\n🧪 Validating ${name}...`)

			const content = fs.readFileSync(file, "utf8")
			const importQueryMatch = content.match(/export const importQuery = `([\s\S]*?)`/)

			if (!importQueryMatch) {
				console.log(`  ⚠️  ${name}: No import query found`)
				continue
			}

			const importQuery = importQueryMatch[1]
			console.log(`  📝 ${name}: Query length: ${importQuery.length} chars`)

			// Detailed syntax validation
			const validationResult = validateQuerySyntax(importQuery, name)

			if (validationResult.valid) {
				console.log(`  ✅ ${name}: Query syntax valid`)
				successCount++
			} else {
				console.log(`  ❌ ${name}: Query syntax errors:`)
				validationResult.errors.forEach((error) => {
					console.log(`     - ${error}`)
				})

				errors.push({
					language: name,
					errors: validationResult.errors,
					query: importQuery,
				})
				errorCount++
			}
		} catch (error) {
			console.log(`  ❌ ${name}: File error - ${error.message}`)
			errors.push({
				language: name,
				errors: [`File error: ${error.message}`],
				query: null,
			})
			errorCount++
		}
	}

	console.log("\n" + "=".repeat(60))
	console.log("📊 QUERY VALIDATION RESULTS")
	console.log("=".repeat(60))

	console.log(`✅ Valid queries: ${successCount}/${queryFiles.length}`)
	console.log(`❌ Invalid queries: ${errorCount}/${queryFiles.length}`)

	if (errors.length > 0) {
		console.log("\n🚨 DETAILED ERROR ANALYSIS:")
		console.log("-".repeat(60))

		errors.forEach((err, index) => {
			console.log(`\n${index + 1}. ${err.language}:`)
			err.errors.forEach((error) => {
				console.log(`   • ${error}`)
			})

			if (err.query) {
				console.log(`   Query preview:`)
				console.log(`   ${err.query.substring(0, 200)}...`)
			}
		})

		console.log("\n🔧 SUGGESTED FIXES:")
		console.log("-".repeat(40))

		// Analyze common error patterns
		const commonErrors = {}
		errors.forEach((err) => {
			err.errors.forEach((error) => {
				const errorType = error.split(":")[0] || error.split(" ")[0]
				if (!commonErrors[errorType]) {
					commonErrors[errorType] = []
				}
				commonErrors[errorType].push(err.language)
			})
		})

		Object.entries(commonErrors).forEach(([errorType, languages]) => {
			console.log(`• ${errorType}: ${languages.join(", ")}`)
		})
	}

	console.log("\n" + "=".repeat(60))
	console.log("🏁 VALIDATION COMPLETED")
	console.log("=".repeat(60))

	return { successCount, errorCount, errors }
}

function validateQuerySyntax(query, language) {
	const errors = []

	// Basic structure validation
	if (!query.trim()) {
		errors.push("Empty query")
		return { valid: false, errors }
	}

	// Check for capture groups
	if (!query.includes("@")) {
		errors.push("No capture groups found (@)")
	}

	// Check parentheses balance
	let parenCount = 0
	let maxDepth = 0
	for (let i = 0; i < query.length; i++) {
		const char = query[i]
		if (char === "(") {
			parenCount++
			maxDepth = Math.max(maxDepth, parenCount)
		} else if (char === ")") {
			parenCount--
			if (parenCount < 0) {
				errors.push(`Unmatched closing parenthesis at position ${i}`)
				break
			}
		}
	}

	if (parenCount > 0) {
		errors.push(`${parenCount} unmatched opening parentheses`)
	}

	// Check for suspicious node types (common mistakes)
	const suspiciousPatterns = [
		{ pattern: /\(asterisk_import/g, issue: "asterisk_import might not be a valid node type" },
		{ pattern: /\(import_list/g, issue: "import_list might not be a valid node type" },
		{ pattern: /\(global_using_directive/g, issue: "global_using_directive might not be a valid node type" },
		{ pattern: /\(name_equals/g, issue: "name_equals might not be a valid node type" },
		{ pattern: /\(scoped_identifier/g, issue: "scoped_identifier usage should be verified" },
		{ pattern: /\(qualified_identifier/g, issue: "qualified_identifier usage should be verified" },
	]

	suspiciousPatterns.forEach(({ pattern, issue }) => {
		const matches = query.match(pattern)
		if (matches) {
			errors.push(`${issue} (found ${matches.length} times)`)
		}
	})

	// Check for field syntax issues
	const fieldPattern = /([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g
	let fieldMatch
	const fields = []
	while ((fieldMatch = fieldPattern.exec(query)) !== null) {
		fields.push(fieldMatch[1])
	}

	// Language-specific validations
	switch (language.toLowerCase()) {
		case "java":
			if (query.includes("asterisk_import")) {
				errors.push("Java: asterisk_import is likely incorrect, should be wildcard_import or similar")
			}
			break
		case "kotlin":
			if (query.includes("import_list")) {
				errors.push("Kotlin: import_list might be incorrect node type")
			}
			break
		case "c#":
			if (query.includes("global_using_directive")) {
				errors.push("C#: global_using_directive might not be supported in all tree-sitter versions")
			}
			break
	}

	// Check capture naming conventions
	const capturePattern = /@([a-zA-Z_][a-zA-Z0-9_.]*)/g
	let captureMatch
	while ((captureMatch = capturePattern.exec(query)) !== null) {
		const captureName = captureMatch[1]
		if (!captureName.startsWith("import.")) {
			errors.push(`Capture '${captureName}' doesn't follow import.* naming convention`)
		}
	}

	return {
		valid: errors.length === 0,
		errors,
	}
}

// Run the validation
validateImportQueries()
	.then((results) => {
		if (results.errorCount > 0) {
			console.log(`\n🔧 Found ${results.errorCount} queries with potential issues`)
			process.exit(1)
		} else {
			console.log("\n🎉 All import queries are syntactically valid!")
			process.exit(0)
		}
	})
	.catch((error) => {
		console.error("💥 Unhandled error:", error)
		process.exit(1)
	})
