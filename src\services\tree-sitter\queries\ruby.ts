/*
- method definitions (including singleton methods and aliases, with associated comments)
- class definitions (including singleton classes, with associated comments)
- module definitions
*/
export const definitionQuery = `
(
  (comment)* @doc
  .
  [
    (method
      (_) @definition.method) @definition.method
    (singleton_method
      (_) @definition.method) @definition.method
  ]
  (#strip! @doc "^#\\s*")
  (#select-adjacent! @doc @definition.method)
)

(alias
  (_) @definition.method) @definition.method

(
  (comment)* @doc
  .
  [
    (class
      name: [
        (constant) @name.definition.class
        (scope_resolution
          (_) @definition.class)
      ]) @definition.class
    (singleton_class
      value: [
        (constant) @name.definition.class
        (scope_resolution
          (_) @definition.class)
      ]) @definition.class
  ]
  (#strip! @doc "^#\\s*")
  (#select-adjacent! @doc @definition.class)
)

(
  (module
    name: [
      (constant) @name.definition.module
      (scope_resolution
        (_) @definition.module)
    ]) @definition.module
)
`

/*
- Ruby require statements and module includes
*/
export const importQuery = `
; require 'module'
(call
  method: (identifier) @import.method
  arguments: (argument_list
    (string) @import.source)) @import.statement
  (#eq? @import.method "require")

; require_relative 'module'
(call
  method: (identifier) @import.method
  arguments: (argument_list
    (string) @import.source)) @import.statement
  (#eq? @import.method "require_relative")

; load 'file.rb'
(call
  method: (identifier) @import.method
  arguments: (argument_list
    (string) @import.source)) @import.statement
  (#eq? @import.method "load")

; include Module
(call
  method: (identifier) @import.method
  arguments: (argument_list
    (constant) @import.name)) @import.statement
  (#eq? @import.method "include")

; extend Module
(call
  method: (identifier) @import.method
  arguments: (argument_list
    (constant) @import.name)) @import.statement
  (#eq? @import.method "extend")

; prepend Module
(call
  method: (identifier) @import.method
  arguments: (argument_list
    (constant) @import.name)) @import.statement
  (#eq? @import.method "prepend")
`

// Default export for backward compatibility
export default definitionQuery
