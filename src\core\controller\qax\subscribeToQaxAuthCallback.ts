/**
 * <AUTHOR>
 */

import { EmptyRequest, String as ProtoString } from "../../../shared/proto/common"
import { StreamingResponseHandler, getRequestRegistry } from "../grpc-handler"
import { Controller } from "../index"

// Keep track of active QAX authCallback subscriptions
const activeQaxAuthCallbackSubscriptions = new Set<StreamingResponseHandler>()

/**
 * Subscribe to QAX authCallback events
 * @param _controller The controller instance (unused)
 * @param _request The empty request (unused)
 * @param responseStream The streaming response handler
 * @param requestId The ID of the request (passed by the gRPC handler)
 */
export async function subscribeToQaxAuthCallback(
	_controller: Controller,
	_request: EmptyRequest,
	responseStream: StreamingResponseHandler,
	requestId?: string,
): Promise<void> {
	// Add this subscription to the active subscriptions
	activeQaxAuthCallbackSubscriptions.add(responseStream)

	// Register cleanup when the connection is closed
	const cleanup = () => {
		activeQaxAuthCallbackSubscriptions.delete(responseStream)
	}

	// Register the cleanup function with the request registry if we have a requestId
	// This ensures proper cleanup and prevents memory leaks
	if (requestId) {
		getRequestRegistry().registerRequest(requestId, cleanup, { type: "qaxAuthCallback_subscription" }, responseStream)
	}
}

/**
 * Send a QAX authCallback event to all active subscribers
 * @param token The JWT token for authentication
 */
export async function sendQaxAuthCallbackEvent(token: string): Promise<void> {
	// Send the event to all active subscribers
	const promises = Array.from(activeQaxAuthCallbackSubscriptions).map(async (responseStream) => {
		try {
			const event: ProtoString = {
				value: token,
			}
			await responseStream(
				event,
				false, // Not the last message
			)
		} catch (error) {
			console.error("Error sending QAX authCallback event:", error)
			// Remove the subscription if there was an error
			activeQaxAuthCallbackSubscriptions.delete(responseStream)
		}
	})

	await Promise.all(promises)
}
