# ContextGatherer Test Suite

This directory contains comprehensive unit tests for the enhanced ContextGatherer functionality, validating all recent modifications to the context extraction system.

## What's Being Tested

### 🔍 **Core Functionality Tests**

1. **Import-based Definitions**
   - Tree-sitter query parsing of import statements
   - LSP resolution of imported symbols
   - Definition extraction from imported modules
   - Support for multiple languages (JS/TS, Python, Rust, Go, Java)

2. **Recently Visited Function Context**
   - Function-level context extraction from recently visited code
   - Intelligent deduplication against imported definitions
   - Proper source tagging and prioritization
   - Comment inclusion with function definitions

3. **Clipboard Content Integration**
   - Smart detection of code vs non-code content
   - Length limiting and content filtering
   - Graceful handling of empty/invalid clipboard content
   - Integration into the main context assembly

4. **Context Prioritization & Deduplication**
   - Correct ordering: imports → LSP → recent edits → recent visits
   - Duplicate detection and removal across sources
   - Source-based prioritization rules
   - Global context limits and caps

5. **Function Comment Inclusion**
   - JSDoc comment extraction (`/** ... */`)
   - Python docstring handling (`"""..."""`)
   - Single-line comment inclusion (`//`, `#`)
   - Multi-line comment support (`/* ... */`)

6. **Edge Cases & Error Handling**
   - Empty files and malformed code
   - Very long files and content limits
   - Missing imports and dependencies
   - Graceful failure scenarios

### 📊 **Output Validation**

The tests explicitly output and validate:

- **Complete CodeContext structure** with all fields populated
- **Definition breakdown by source** (import, recent_visit, lsp, etc.)
- **Content previews** showing actual extracted code
- **Prioritization verification** ensuring correct ordering
- **Deduplication confirmation** with no duplicate functions
- **Length limit compliance** respecting global caps

## Running the Tests

### Method 1: Automatic (Extension Startup)
The tests run automatically when the extension starts up (after a 3-second delay):

```bash
# Start VS Code with the extension
code .
# Check the Developer Console for test output
```

### Method 2: Manual Execution
Run the test suite directly:

```bash
# From the project root
npx ts-node src/services/autocomplete/test/runTests.ts

# Or compile and run
npm run compile
node out/services/autocomplete/test/runTests.js
```

### Method 3: Integration Testing
Import and run in your own test files:

```typescript
import { runContextGathererTests } from './src/services/autocomplete/test/ContextGathererTest'

async function myTests() {
    await runContextGathererTests()
}
```

## Test Output Format

### Summary Output
```
🧪 CONTEXT GATHERER TEST RESULTS
================================================================================
📊 SUMMARY: 15/17 tests passed (88.2%)
🎉 ALL TESTS PASSED!

📋 DETAILED RESULTS:
--------------------------------------------------------------------------------

Import Definitions: 3/3 (100.0%)
  ✅ Import Definitions - JavaScript ES6 Imports
     Expected imports: React, useState, useEffect, utils, fs. Found: React, useState, useEffect. Import definitions count: 3
  ✅ Import Definitions - Python Imports
     Expected imports: os, sys, List, Dict, helper_function, np. Found: os, sys, helper_function. Import definitions count: 3
```

### Detailed Context Output
```
📄 SAMPLE CONTEXT OUTPUT:
--------------------------------------------------------------------------------
📍 Current Line: "const result = helper(count)"
📄 Preceding Lines: 8
📄 Following Lines: 6
📦 Import Strings: 2
🔍 Total Definitions: 5
📋 Clipboard Content: Present

🔍 DEFINITION BREAKDOWN:
  import: 2 definitions
    1. function useState(initialState) { return [state, setState] }...
    2. function helper(x, y) { return x + y }...
  recent_visit: 1 definitions
    1. function Button({ onClick, children }) { return <button>...
```

## Test Scenarios

### Import Testing
- **JavaScript/TypeScript**: ES6 imports, CommonJS require, type imports
- **Python**: from/import statements, relative imports, aliases
- **Rust**: use statements, extern crate declarations
- **Go**: import statements with aliases
- **Java**: import statements, static imports

### Context Assembly Testing
- **Realistic code files** with multiple import types
- **Function positioning** testing (cursor in different locations)
- **Mixed content scenarios** (imports + functions + comments)
- **Large file handling** with content limits

### Edge Case Coverage
- Empty files and malformed syntax
- Very long files exceeding limits
- Missing dependencies and broken imports
- Invalid clipboard content

## Expected Results

### ✅ **Passing Criteria**
- All import statements are parsed and resolved
- Recently visited functions are extracted without duplicating imports
- Clipboard content is intelligently filtered and included
- Context is properly prioritized and deduplicated
- Function comments are included in extracted definitions
- Edge cases are handled gracefully without crashes
- Final context structure matches CodeContext interface

### ❌ **Failure Indicators**
- Import parsing fails or returns empty results
- Duplicate functions appear from different sources
- Clipboard content is incorrectly included/excluded
- Context ordering doesn't follow priority rules
- Function comments are missing from definitions
- Edge cases cause crashes or errors
- Context structure is malformed or incomplete

## Troubleshooting

### Common Issues
1. **Tree-sitter parsing failures**: Check that parsers are loaded correctly
2. **LSP resolution errors**: Ensure mock LSP providers are working
3. **Clipboard access issues**: Verify VSCode API mocking
4. **Import query failures**: Check query file syntax and loading

### Debug Output
Enable verbose logging by setting debug flags in the test files:

```typescript
// In ContextGathererTest.ts
const DEBUG = true // Set to true for verbose output
```

This will show detailed parsing steps, query execution, and context assembly process.
