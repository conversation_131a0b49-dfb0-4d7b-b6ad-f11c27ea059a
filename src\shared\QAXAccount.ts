/**
 * <AUTHOR>
 */

// QAX Account 类型定义 - 简化版本
export interface QAXUserInfo {
	userId?: string
	username?: string
	email?: string
	displayName?: string
	employeeNumber?: string
	lastLoginAt?: number
}

export interface QAXJWTPayload {
	sub: string // user ID
	name: string // 用户名
	display_name: string // 显示名称
	email: string
	employee_number: string // 员工编号
	iss: string // 签发者
	aud: string[] // 受众
	iat: number // 签发时间
	exp: number // 过期时间
}

export interface QAXAuthConfig {
	authUrl: string
	apiUrl: string
	clientId: string
	redirectUri: string
}
