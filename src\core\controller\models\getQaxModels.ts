import { Controller } from ".."
import { StringArray } from "../../../shared/proto/common"
import { OpenAiModelsRequest } from "../../../shared/proto/models"
import { QAXConfig } from "../../../utils/qax-config"

/**
 * Fetches available models from QAX
 * @param controller The controller instance
 * @param request The request containing API key and base URL
 * @returns Array of QAX model names
 */
export async function getQaxModels(controller: Controller, request: OpenAiModelsRequest): Promise<StringArray> {
	try {
		const apiKey = request.apiKey

		if (!apiKey) {
			return StringArray.create({ values: [] })
		}

		const response = await fetch(QAXConfig.getQaxModelsApiUrl(), {
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${apiKey}`,
			},
		})

		if (!response.ok) {
			console.warn(`Failed to fetch QAX models (HTTP ${response.status})`)
			return StringArray.create({ values: [] })
		}

		const data = await response.json()
		const modelsData = data.data || []

		// Extract model IDs from the [displayName, modelId] format
		const modelIds = modelsData.map((model: [string, string]) => model[1])

		return StringArray.create({ values: modelIds })
	} catch (error) {
		console.warn("Failed to fetch QAX models:", error)
		return StringArray.create({ values: [] })
	}
}
